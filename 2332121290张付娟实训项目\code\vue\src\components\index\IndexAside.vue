<template>
  <el-aside class="index-aside" width="200px">
    <div class="index-aside-inner">
      <el-menu default-active="1">
        <el-menu-item @click="menuHandler('/')" index="1">
          首页
        </el-menu-item>
        <sub-menu
          v-for="menu in menuList"
          :key="menu.menuId"
          :menu="menu"
          :dynamicMenuRoutes="dynamicMenuRoutes"
        ></sub-menu>
      </el-menu>
    </div>
  </el-aside>
</template>
<script>
import SubMenu from "@/components/index/IndexAsideSub";
export default {
  data() {
    return {
      menuList: [],
      dynamicMenuRoutes: []
    };
  },
  components: {
    SubMenu
  },
  mounted() {
    // 获取动态菜单数据并且渲染
    this.menuList = JSON.parse(sessionStorage.getItem("menuList") || "[]");
    this.dynamicMenuRoutes = JSON.parse(
      sessionStorage.getItem("dynamicMenuRoutes") || "[]"
    );
  },
  methods: {
    menuHandler(path) {
      this.$router.push({ path: path });
    }
  }
};
</script>
<style lang="scss" scoped>
.index-aside {
  margin-top: 80px;
  overflow: hidden;
  
  .index-aside-inner {
    width: 217px;
    height: 100%;
    overflow-y: scroll;
    
    .el-menu {
      background-color: transparent;
      border-right: none;
      
      .el-menu-item {
        color: #fff;
        background-color: var(--publicMainColor) !important;
        margin-bottom: 2px;
        border-radius: 4px;
        
        &:hover, &:focus {
          background-color: var(--publicSubColor) !important;
          color: #fff;
        }
        
        &.is-active {
          background-color: var(--publicSubColor) !important;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
}
</style>

