.form-content {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
.table-content {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.pagination-content {
  margin-top: 20px;
  padding-bottom: 20px;
  text-align: right;
}
.detail-form-content {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-input {
    min-width: 200px;
    max-width: 600px;
  }
}
.bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: center;
}
.login-form {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  width: 400px;
  background: #ffffff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
  font-size: 16px;
}
.h1 {
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #333;
}
.btn-login {
  margin-top: 30px;
  width: 100%;
  height: 44px;
  font-size: 16px;
  letter-spacing: 1px;
}

// 列表页面样式
.search-form {
  margin-bottom: 20px;
}

// 表格美化
.el-table {
  border-radius: 4px;
  overflow: hidden;
  
  th {
    background-color: #f5f7fa !important;
    color: #333;
    font-weight: bold;
  }
  
  td {
    padding: 12px 0;
  }
}

// 按钮样式统一
.el-button {
  border-radius: 4px;
  
  &.el-button--primary {
    border: none;
    
    &:hover, &:focus {
      opacity: 0.9;
    }
  }
}

// 卡片样式
.el-card {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

/* 全局溢出控制 */
html, body {
  overflow-x: hidden;
  position: relative;
  width: 100%;
}

#app, .el-container, .el-main {
  overflow-x: hidden;
  max-width: 100vw;
}

/* 防止任何隐藏框架显示 */
body::after, 
body::before,
#app::after,
#app::before {
  content: none !important;
  display: none !important;
}

/* 消除可能的横向滚动条 */
::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

/* 全局按钮样式修改 */
.el-button--success,
.el-button--danger,
.el-button--primary,
.el-button--warning,
.el-button--info {
  /* 轻微圆角 */
  border-radius: 4px !important;
  /* 保留细边框增加精致感 */
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  /* 扁平化设计 */
  box-shadow: none !important;
  /* 简单过渡效果 */
  transition: all 0.2s ease !important;
  /* 确保按钮内容居中 */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  /* 调整内边距 */
  padding: 8px 16px !important;
  font-weight: normal !important;
}

/* 按钮悬停效果 */
.el-button--success:hover,
.el-button--danger:hover,
.el-button--primary:hover,
.el-button--warning:hover,
.el-button--info:hover {
  opacity: 0.9 !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
}

/* 按钮点击效果 */
.el-button--success:active,
.el-button--danger:active,
.el-button--primary:active,
.el-button--warning:active,
.el-button--info:active {
  opacity: 1 !important;
  transform: scale(0.98) !important;
}

/* 基于系统主题的按钮颜色 */
.el-button--primary {
  background-color: #4d96ff !important; 
  color: white !important;
}

.el-button--success {
  background-color: #5bba6f !important; 
  color: white !important;
}

.el-button--danger {
  background-color: #ff7676 !important; 
  color: white !important;
}

.el-button--warning {
  background-color: #ffb347 !important; 
  color: white !important;
}

.el-button--info {
  background-color: #8aa9c2 !important; 
  color: white !important;
}

/* 查询按钮特殊样式 */
.el-button.is-plain.el-button--primary, 
.el-button.el-button--primary:has(.el-icon-search) {
  background-color: #5bba6f !important; 
  color: white !important;
}