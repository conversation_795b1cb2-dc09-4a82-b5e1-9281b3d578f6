/*定义全局css*/
body {
	/* 1 全局公共主颜色 */
	--publicMainColor: #9ae2d4;
	/* 1 全局公共副颜色 */
	--publicSubColor:  #54C0CC;
}
.bodyClass{
}

/*开始==================================导航栏样式10=========================================开始*/
    .homeHeight{
        height: 88vh !important;
    }
   #iframe {
        width: 100%;
        margin-top: 50px;
        padding-top: 74px;
    }
    #header {
        height: auto;
        background: #fff;
        border-bottom: 0;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1;
    }

    #header .nav-top {
        display: flex;
        align-items: center;
        padding: 0 20px;
        font-size: 16px;
        color: #86efef;
        box-sizing: border-box;
        height: 50px;
        background: var(--publicMainColor);
        box-shadow: 0 0px 0px rgba(0,0,0,.3);
        justify-content: center;
        position: relative;
        z-index: 1;
    }

    #header .nav-top-img {
        width: 124px;
        height: 40px;
        padding: 0;
        margin: 0;
        border-radius: 6px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        box-shadow: 0 0 6px rgba(0,0,0,.3);
    }

    #header .nav-top-title {
        line-height: 40px;
        font-size: 24px;
        color: rgba(255, 255, 255, 1);
        padding: 0 10px;
        margin: 0 10px;
        border-radius: 6px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        background-color: rgba(0,0,0,0);
        box-shadow: 0 0 0px rgba(0,0,0,.3);
    }

    #header .nav-top-tel {
        line-height: 40px;
        font-size: 16px;
        color: #333;
        padding: 0 10px;
        margin: 0;
        border-radius: 6px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        box-shadow: 0 0 6px rgba(0,0,0,.3);
    }

    #header .navs {
        display: flex;
        padding: 0 20px;
        align-items: center;
        box-sizing: border-box;
        height: 74px;
        background: #fff;
        box-shadow: 0 1px 6px rgba(0,0,0,0);
        justify-content: center;
    }
    #header .navs1 {
        width: 200px;
        height: 100vh;
        background: darkgoldenrod;
        position: absolute;
        top: 0;
        display: block;
        padding: 0;
        overflow-y: scroll;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    #header .navs.navs1::-webkit-scrollbar {
        display: none;
    }

    #header .navs1 .list {
        display: flex;
        flex-direction: column;
    }
    #header .navs .title {
        width: auto;
        line-height: 40px;
        font-size: 16px;
        color: #333;
        padding: 0 10px;
        margin: 0 5px;
        border-radius: 6px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        background-color: rgba(0,0,0,0);
        box-shadow: 0 0 6px rgba(0,0,0,0);
    }
    #header .navs li {
        display: inline-block;
        width: 110px;
        line-height: 34px;
        padding: 0 10px;
        margin: 0 5px;
        color: rgba(152, 152, 152, 1);
        font-size: 16px;
        border-radius: 0;
        border-width: 0 0 6px;
        border-style: solid;
        border-color: rgba(220, 220, 220, 1);
        background-color: rgba(247, 247, 247, 0);
        box-shadow: 0 6px 0px #989898;
        text-align: center;
        box-sizing: border-box;
        cursor: pointer;
    }
    #header .navs li a{
        color: inherit;
    }
    #header .navs li.current a{
        color: inherit;
    }
    #header .navs li a:hover{
        color: inherit;
    }
    #header .navs li.current {
        color: var(--publicSubColor);
        font-size: 16px;
        border-radius: 0;
        border-width: 0 0 6px;
        border-style: solid;
        border-color: rgba(188, 203, 213, 1);
        background-color: rgba(247, 247, 247, 0);
        box-shadow: 0 6px 0 var(--publicMainColor);
    }
    #header .navs li:hover {
        color: var(--publicSubColor);
        font-size: 16px;
        border-radius: 0;
        border-width: 0 0 6px;
        border-style: solid;
        border-color: rgba(188, 203, 213, 1);
        background-color: rgba(255, 255, 255, 0);
        box-shadow: 0 6px 0px var(--publicMainColor);
    }
/*结束==================================导航栏样式10=========================================结束*/

/*layui下拉框边框颜色*/
.layui-unselect{
	border-color: var(--publicMainColor)
}

/*home页面数据样式 开始*/
	/*home页面数据样式 普通数据样式 开始*/
	/*home页面数据样式 普通数据样式 结束*/
/*home页面数据样式 结束*/

/*list页面数据样式 开始*/
	/*list页面数据样式 普通数据样式 开始*/
.probootstrap-animate {
	/*opacity: 0;
	visibility: hidden;*/
}
.probootstrap-thumbnail {
	position: relative;
	display: block;
	-webkit-transition: .3s all ease;
	-o-transition: .3s all ease;
	transition: .3s all ease;
	opacity: 1;
	width: 80%;
}
.probootstrap-thumbnail.sleep {
    opacity: .5;
}
.probootstrap-thumbnail img {
    position: relative;
    -webkit-transition: .3s all ease;
    -o-transition: .3s all ease;
    transition: .3s all ease;
    display: block;
    bottom: 0;
    z-index: 2;
	width: 100%;
	height: 290px;
}
.probootstrap-thumbnail h4 {
	z-index: 1;
	display: block;
	text-align: center;
	position: relative;
	-webkit-transition: .2s all ease;
	-o-transition: .2s all ease;
	transition: .2s all ease;
	opacity: 0;
	visibility: hidden;
	bottom: 30px;
	color: var(--publicMainColor,orange);
}
	@media (max-width: 991px) {
		.probootstrap-thumbnail h4 {
			font-size: 24px;
			bottom: 0;
			color: var(--publicMainColor,orange);
		}
	}
	@media (max-width: 767px) {
		.probootstrap-thumbnail h4 {
			opacity: 1;
			visibility: visible;
			bottom: 0;
			margin-top: 10px;
			color: var(--publicMainColor,orange);
		}
	}
.probootstrap-thumbnail:hover h4 {
	opacity: 1;
	visibility: visible;
	bottom: -5px;
	color: var(--publicMainColor,orange);
}
    @media (max-width: 991px) {
		.probootstrap-thumbnail:hover h4 {
			bottom: 0px;
			color: var(--publicMainColor,orange);
		}
	}
.probootstrap-thumbnail:hover img {
	-webkit-transform: scale(1.08);
	-ms-transform: scale(1.08);
	transform: scale(1.08);
	bottom: 20px;
	-webkit-box-shadow: 0 10px 20px -10px rgba(0, 0, 0, 0.2);
	box-shadow: 0 10px 20px -10px rgba(0, 0, 0, 0.2);
	width: 100%;
	height: 290px;
}
	@media (max-width: 991px) {
		.probootstrap-thumbnail:hover img {
			bottom: 20px;
			-webkit-transform: scale(1);
			-ms-transform: scale(1);
			transform: scale(1);
		}
	}
	@media (max-width: 767px) {
		.probootstrap-thumbnail:hover img {
			bottom: 0px;
			opacity: 1;
			visibility: visible;
		}
	}
.mb-3 {
	margin-bottom: 1rem !important;
}
.mb-4 {
	margin-bottom: 1.5rem !important;
}
.img-fluid {
	max-width: 100%;
	height: auto;
}
	/*list页面数据样式 普通数据样式 结束*/
/*list页面数据样式 结束*/

/*鼠标移入样式 开始*/
.animation-box:hover {
    transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.2) rotate(2deg) skew(2deg, 2deg);
    transition: all 0.3s;
}/*鼠标移入样式 结束*/


/* 主页 轮播图选择框颜色 主*/
#test1 .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 个人中心轮播图 */
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}

/* 大部分颜色 主 */
.main_color {
	color: var(--publicMainColor, #808080);
}
/* 边框颜色 主 */
.main_borderColor{
	border-color: var(--publicMainColor, #808080);
}
/* 背景颜色 主 */
.main_backgroundColor {
	background-color: var(--publicMainColor, #808080);
}
/* 登录页面单选按钮颜色 主 */
.l-redio .layui-form-radioed>i {
	font-size: 16px;
	color: var(--publicMainColor, #808080);
}
.l-redio .layui-form-radioed>div {
	font-size: 14px;
	color: var(--publicMainColor, #808080);
}

/* 大部分颜色 副 */
.sub_color {
	color: var(--publicSubColor, #808080);
}
/* 边框颜色 副 */
.sub_borderColor{
	border-color: var(--publicSubColor, #808080);
	box-shadow: 0 0 6px var(--publicSubColor, #808080);
}
/* 背景颜色 副 */
.sub_backgroundColor {
	background-color: var(--publicSubColor, #808080);
}

/* 分页颜色 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: var(--publicMainColor, #808080);
}

/* 评论和简介背景颜色 */
.detail-tab .layui-tab-card>.layui-tab-title .layui-this {
	background-color: var(--publicMainColor, #808080);
	color: #fff;
	font-size: 14px;
}
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
}

/* 个人中心 菜单点击颜色*/
.center-container .layui-nav-tree .layui-nav-item.layui-this {
	background-color: var(--publicSubColor, #808080);
}
/*个人中心 菜单鼠标移上颜色*/
.center-container .layui-nav-tree .layui-nav-item:hover {
	background-color:var(--publicMainColor, #808080);
}
/*个人中心 菜单下线颜色*/
.center-container .layui-nav-tree .layui-nav-item {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 输入框中字体颜色和边框颜色*/
.right-container .input .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 下拉框中字体颜色和边框颜色*/
.right-container .select .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 未知颜色*/
.right-container .date .layui-input {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}

/* 前台elementUI得下拉框内容颜色和边框颜色修改 */
/* start */
.el-select-dropdown__item.selected {
	color: var(--publicMainColor, #808080);
	font-weight: bold;
}
.el-select .el-input.is-focus .el-input__inner {
	border-color: var(--publicMainColor, #808080);
}
.el-input--suffix .el-input__inner{
	color:var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
}
.el-select .el-input__inner:focus {
	border-color: var(--publicMainColor, #808080);
}
/* end */
/*=====================富文本框字体样式===========================================================================*/

.ql-size-small {
	font-size: 10px;
}
.ql-size-large {
	font-size: 18px;
}
.ql-size-huge {
	font-size: 32px;
}