<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ForumDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.forum_name as forumName
        ,a.yonghu_id as yonghuId
        ,a.shangjia_id as shangjiaId
        ,a.users_id as usersId
        ,a.forum_content as forumContent
        ,a.super_ids as superIds
        ,a.forum_state_types as forumStateTypes
        ,a.insert_time as insertTime
        ,a.update_time as updateTime
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ForumView" >
        SELECT
        <include refid="Base_Column_List" />
        <!-- 级联表的字段 -->
        ,shangjia.shangjia_name as shangjia<PERSON>ame
        ,shangjia.shangjia_phone as shangjiaPhone
        ,shangjia.shangjia_email as shangjiaEmail
        ,shangjia.shangjia_photo as shangjiaPhoto
        ,shangjia.shangjia_xingji_types as shangjiaXingjiTypes
        ,shangjia.new_money as newMoney
        ,shangjia.shangjia_content as shangjiaContent
        ,shangjia.shangjia_delete as shangjiaDelete
        ,yonghu.yonghu_uuid_number as yonghuUuidNumber
        ,yonghu.yonghu_name as yonghuName
        ,yonghu.yonghu_phone as yonghuPhone
        ,yonghu.yonghu_id_number as yonghuIdNumber
        ,yonghu.yonghu_photo as yonghuPhoto
        ,yonghu.yonghu_email as yonghuEmail
        ,yonghu.new_money as newMoney
        ,users.username as uusername
        ,users.password as upassword
        ,users.role as urole
        ,users.addtime as uaddtime

        FROM forum  a
        left JOIN shangjia shangjia ON a.shangjia_id = shangjia.id
        left JOIN yonghu yonghu ON a.yonghu_id = yonghu.id
        left JOIN users users ON a.users_id = users.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.forumName != '' and params.forumName != null and params.forumName != 'null' ">
                and a.forum_name like CONCAT('%',#{params.forumName},'%')
            </if>
            <if test="params.yonghuId != null and params.yonghuId != ''">
                and (
                    a.yonghu_id = #{params.yonghuId}
                )
            </if>
            <if test="params.shangjiaId != null and params.shangjiaId != ''">
                and (
                    a.shangjia_id = #{params.shangjiaId}
                )
            </if>
            <if test="params.usersId != null and params.usersId != ''">
                and (
                    a.users_id = #{params.usersId}
                )
            </if>
            <if test=" params.forumContent != '' and params.forumContent != null and params.forumContent != 'null' ">
                and a.forum_content like CONCAT('%',#{params.forumContent},'%')
            </if>
            <if test="params.superIdsStart != null and params.superIdsStart != ''">
                <![CDATA[  and a.super_ids >= #{params.superIdsStart}   ]]>
            </if>
            <if test="params.superIdsEnd != null and params.superIdsEnd != ''">
                <![CDATA[  and a.super_ids <= #{params.superIdsEnd}   ]]>
            </if>
             <if test="params.superIds != null and params.superIds != ''">
                and a.super_ids = #{params.superIds}
             </if>
            <if test="params.forumStateTypes != null and params.forumStateTypes != ''">
                and a.forum_state_types = #{params.forumStateTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test="params.insertTime != null and params.insertTime != ''">
                and a.insert_time = #{params.insertTime}
            </if>
            <if test=" params.updateTimeStart != '' and params.updateTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) >= UNIX_TIMESTAMP(#{params.updateTimeStart}) ]]>
            </if>
            <if test=" params.updateTimeEnd != '' and params.updateTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) <= UNIX_TIMESTAMP(#{params.updateTimeEnd}) ]]>
            </if>
            <if test="params.updateTime != null and params.updateTime != ''">
                and a.update_time = #{params.updateTime}
            </if>

                <!-- 判断商家的id不为空 -->
            <if test=" params.shangjiaIdNotNull != '' and params.shangjiaIdNotNull != null and params.shangjiaIdNotNull != 'null' ">
                and a.shangjia_id IS NOT NULL
            </if>
            <if test=" params.shangjiaName != '' and params.shangjiaName != null and params.shangjiaName != 'null' ">
                and shangjia.shangjia_name like CONCAT('%',#{params.shangjiaName},'%')
            </if>
            <if test=" params.shangjiaPhone != '' and params.shangjiaPhone != null and params.shangjiaPhone != 'null' ">
                and shangjia.shangjia_phone like CONCAT('%',#{params.shangjiaPhone},'%')
            </if>
            <if test=" params.shangjiaEmail != '' and params.shangjiaEmail != null and params.shangjiaEmail != 'null' ">
                and shangjia.shangjia_email like CONCAT('%',#{params.shangjiaEmail},'%')
            </if>
            <if test="params.shangjiaXingjiTypes != null  and params.shangjiaXingjiTypes != ''">
                and shangjia.shangjia_xingji_types = #{params.shangjiaXingjiTypes}
            </if>

            <if test="params.newMoneyStart != null ">
                <![CDATA[  and shangjia.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and shangjia.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.newMoney != null and params.newMoney != ''">
                and a.new_money = #{params.newMoney}
            </if>
            <if test=" params.shangjiaContent != '' and params.shangjiaContent != null and params.shangjiaContent != 'null' ">
                and shangjia.shangjia_content like CONCAT('%',#{params.shangjiaContent},'%')
            </if>
            <if test="params.shangjiaDeleteStart != null  and params.shangjiaDeleteStart != '' ">
                <![CDATA[  and shangjia.shangjia_delete >= #{params.shangjiaDeleteStart}   ]]>
            </if>
            <if test="params.shangjiaDeleteEnd != null  and params.shangjiaDeleteEnd != '' ">
                <![CDATA[  and shangjia.shangjia_delete <= #{params.shangjiaDeleteEnd}   ]]>
            </if>
            <if test="params.shangjiaDelete != null  and params.shangjiaDelete != '' ">
                and shangjia.shangjia_delete = #{params.shangjiaDelete}
            </if>
                <!-- 判断用户的id不为空 -->
            <if test=" params.yonghuIdNotNull != '' and params.yonghuIdNotNull != null and params.yonghuIdNotNull != 'null' ">
                and a.yonghu_id IS NOT NULL
            </if>
            <if test=" params.yonghuUuidNumber != '' and params.yonghuUuidNumber != null and params.yonghuUuidNumber != 'null' ">
                and yonghu.yonghu_uuid_number like CONCAT('%',#{params.yonghuUuidNumber},'%')
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and yonghu.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and yonghu.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuIdNumber != '' and params.yonghuIdNumber != null and params.yonghuIdNumber != 'null' ">
                and yonghu.yonghu_id_number like CONCAT('%',#{params.yonghuIdNumber},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and yonghu.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.newMoneyStart != null ">
                <![CDATA[  and yonghu.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and yonghu.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.newMoney != null and params.newMoney != ''">
                and a.new_money = #{params.newMoney}
            </if>
                <!-- 判断管理员的id不为空 -->
            <if test=" params.usersIdNotNull != '' and params.usersIdNotNull != null and params.usersIdNotNull != 'null' ">
                and a.users_id IS NOT NULL
            </if>
            <if test=" params.username != '' and params.username != null and params.username != 'null' ">
                and users.username like CONCAT('%',#{params.username},'%')
            </if>
            <if test=" params.password != '' and params.password != null and params.password != 'null' ">
                and users.password like CONCAT('%',#{params.password},'%')
            </if>
            <if test=" params.role != '' and params.role != null and params.role != 'null' ">
                and users.role like CONCAT('%',#{params.role},'%')
            </if>
            <if test=" params.addtimeStart != '' and params.addtimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(users.addtime) >= UNIX_TIMESTAMP(#{params.addtimeStart}) ]]>
            </if>
            <if test=" params.addtimeEnd != '' and params.addtimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(users.addtime) <= UNIX_TIMESTAMP(#{params.addtimeEnd}) ]]>
            </if>
            <if test="params.addtime != null and params.addtime != ''">
                and a.addtime = #{params.addtime}
            </if>
        </where>
        order by a.${params.sort} ${params.order}
    </select>

</mapper>