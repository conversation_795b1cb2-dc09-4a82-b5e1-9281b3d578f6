/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80017 (8.0.17)
 Source Host           : localhost:3306
 Source Schema         : nongchanpinxiaoshou

 Target Server Type    : MySQL
 Target Server Version : 80017 (8.0.17)
 File Encoding         : 65001

 Date: 24/06/2025 18:06:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ',
  `yonghu_id` int(11) NOT NULL COMMENT '创建用户',
  `address_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '收货人 ',
  `address_phone` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '电话 ',
  `address_dizhi` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '地址 ',
  `isdefault_types` int(11) NOT NULL COMMENT '是否默认地址 ',
  `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 show3',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '收货地址' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of address
-- ----------------------------
INSERT INTO `address` VALUES (1, 3, '收货人1', '17703786901', '地址1', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (2, 3, '收货人2', '17703786902', '地址2', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (3, 1, '收货人3', '17703786903', '地址3', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (4, 3, '收货人4', '17703786904', '地址4', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (5, 2, '收货人5', '17703786905', '地址5', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (6, 3, '收货人6', '17703786906', '地址6', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (7, 1, '收货人7', '17703786907', '地址7', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (8, 2, '收货人8', '17703786908', '地址8', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (9, 3, '收货人9', '17703786909', '地址9', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (10, 1, '收货人10', '17703786910', '地址10', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (11, 1, '收货人11', '17703786911', '地址11', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (12, 1, '收货人12', '17703786912', '地址12', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (13, 3, '收货人13', '17703786913', '地址13', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (14, 2, '收货人14', '17703786914', '地址14', 1, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');
INSERT INTO `address` VALUES (15, 4, '张三', '13500000000', '南京溧水', 2, '2025-06-22 14:21:55', '2025-06-22 14:21:47', '2025-06-22 14:21:38');

-- ----------------------------
-- Table structure for cart
-- ----------------------------
DROP TABLE IF EXISTS `cart`;
CREATE TABLE `cart`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yonghu_id` int(11) NULL DEFAULT NULL COMMENT '所属用户',
  `nongchanpin_id` int(11) NULL DEFAULT NULL COMMENT '农产品',
  `buy_number` int(11) NULL DEFAULT NULL COMMENT '购买数量',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '购物车' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cart
-- ----------------------------

-- ----------------------------
-- Table structure for config
-- ----------------------------
DROP TABLE IF EXISTS `config`;
CREATE TABLE `config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配置参数名称',
  `value` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配置参数值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置文件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config
-- ----------------------------
INSERT INTO `config` VALUES (1, '轮播图1', '/upload/1750759428851.jpg');
INSERT INTO `config` VALUES (2, '轮播图2', '/upload/1750759440527.jpg');
INSERT INTO `config` VALUES (3, '轮播图3', '/upload/1750759452589.jpg');

-- ----------------------------
-- Table structure for dictionary
-- ----------------------------
DROP TABLE IF EXISTS `dictionary`;
CREATE TABLE `dictionary`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dic_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段',
  `dic_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段名',
  `code_index` int(11) NULL DEFAULT NULL COMMENT '编码',
  `index_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码名字  Search111 ',
  `super_id` int(11) NULL DEFAULT NULL COMMENT '父字段id',
  `beizhu` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dictionary
-- ----------------------------
INSERT INTO `dictionary` VALUES (1, 'sex_types', '性别类型', 1, '男', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (2, 'sex_types', '性别类型', 2, '女', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (3, 'gonggao_types', '公告类型', 1, '公告类型1', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (4, 'gonggao_types', '公告类型', 2, '公告类型2', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (5, 'forum_state_types', '帖子状态', 1, '发帖', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (6, 'forum_state_types', '帖子状态', 2, '回帖', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (7, 'isdefault_types', '是否默认地址', 1, '否', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (8, 'isdefault_types', '是否默认地址', 2, '是', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (9, 'shangxia_types', '上下架', 1, '上架', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (10, 'shangxia_types', '上下架', 2, '下架', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (11, 'nongchanpin_types', '农产品类型', 1, '农产品类型1', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (12, 'nongchanpin_types', '农产品类型', 2, '农产品类型2', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (13, 'nongchanpin_types', '农产品类型', 3, '农产品类型3', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (14, 'nongchanpin_types', '农产品类型', 4, '农产品类型4', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (15, 'nongchanpin_collection_types', '收藏表类型', 1, '收藏', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (16, 'nongchanpin_order_types', '订单类型', 101, '已支付', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (17, 'nongchanpin_order_types', '订单类型', 102, '已退款', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (18, 'nongchanpin_order_types', '订单类型', 103, '已发货', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (19, 'nongchanpin_order_types', '订单类型', 104, '已收货', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (20, 'nongchanpin_order_types', '订单类型', 105, '已评价', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (21, 'shangjia_xingji_types', '商家信用类型', 1, '一级', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (22, 'shangjia_xingji_types', '商家信用类型', 2, '二级', NULL, NULL, '2025-06-22 14:21:05');
INSERT INTO `dictionary` VALUES (23, 'shangjia_xingji_types', '商家信用类型', 3, '三级', NULL, NULL, '2025-06-22 14:21:05');

-- ----------------------------
-- Table structure for forum
-- ----------------------------
DROP TABLE IF EXISTS `forum`;
CREATE TABLE `forum`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `forum_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '帖子标题  Search111 ',
  `yonghu_id` int(11) NULL DEFAULT NULL COMMENT '用户',
  `shangjia_id` int(11) NULL DEFAULT NULL COMMENT '商家',
  `users_id` int(11) NULL DEFAULT NULL COMMENT '管理员',
  `forum_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发布内容',
  `super_ids` int(11) NULL DEFAULT NULL COMMENT '父id',
  `forum_state_types` int(11) NULL DEFAULT NULL COMMENT '帖子状态',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '发帖时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间 show2',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '交流论坛' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of forum
-- ----------------------------
INSERT INTO `forum` VALUES (1, '帖子标题1', 3, NULL, NULL, '发布内容1', 66, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (2, '帖子标题2', 3, NULL, NULL, '发布内容2', 256, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (3, '帖子标题3', 3, NULL, NULL, '发布内容3', 500, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (4, '帖子标题4', 3, NULL, NULL, '发布内容4', 244, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (5, '帖子标题5', 1, NULL, NULL, '发布内容5', 463, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (6, '帖子标题6', 3, NULL, NULL, '发布内容6', 88, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (7, '帖子标题7', 3, NULL, NULL, '发布内容7', 18, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (8, '帖子标题8', 3, NULL, NULL, '发布内容8', 350, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (9, '帖子标题9', 2, NULL, NULL, '发布内容9', 164, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (10, '帖子标题10', 1, NULL, NULL, '发布内容10', 14, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (11, '帖子标题11', 3, NULL, NULL, '发布内容11', 22, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (12, '帖子标题12', 1, NULL, NULL, '发布内容12', 4, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (13, '帖子标题13', 2, NULL, NULL, '发布内容13', 207, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (14, '帖子标题14', 3, NULL, NULL, '发布内容14', 14, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (17, NULL, NULL, 1, NULL, '测试', 16, 2, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (18, NULL, NULL, NULL, 1, '车阿萨德', 16, 2, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (19, '采购', 4, NULL, NULL, '<p>采购</p>', NULL, 1, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');
INSERT INTO `forum` VALUES (20, NULL, NULL, 1, NULL, '好的', 19, 2, '2025-06-22 14:20:32', '2025-06-22 14:20:24', '2025-06-22 14:20:11');

-- ----------------------------
-- Table structure for gonggao
-- ----------------------------
DROP TABLE IF EXISTS `gonggao`;
CREATE TABLE `gonggao`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ',
  `gonggao_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公告名称 Search111  ',
  `gonggao_photo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公告图片 ',
  `gonggao_types` int(11) NOT NULL COMMENT '公告类型 Search111 ',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `gonggao_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '公告详情 ',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间 show1 show2 nameShow',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '公告信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gonggao
-- ----------------------------
INSERT INTO `gonggao` VALUES (1, '公告名称1', '/upload/1750759414910.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情1</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (2, '公告名称2', '/upload/1750759392935.jpg', 2, '2025-06-22 14:19:23', '<p>公告详情2</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (3, '公告名称3', '/upload/1750759373961.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情3</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (4, '公告名称4', '/upload/1750759360504.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情4</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (5, '公告名称5', '/upload/1750759343205.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情5</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (6, '公告名称6', '/upload/1750759328661.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情6</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (7, '公告名称7', '/upload/1750759313474.jpg', 2, '2025-06-22 14:19:23', '<p>公告详情7</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (8, '公告名称8', '/upload/1750759299128.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情8</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (9, '公告名称9', '/upload/1750759286059.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情9</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (10, '公告名称10', '/upload/1750759272889.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情10</p>', '2025-06-22 14:19:03');
INSERT INTO `gonggao` VALUES (11, '公告名称11', '/upload/1750759260618.jpg', 1, '2025-06-22 14:19:23', '<p>公告详情11</p>', '2025-06-22 14:19:03');

-- ----------------------------
-- Table structure for nongchanpin
-- ----------------------------
DROP TABLE IF EXISTS `nongchanpin`;
CREATE TABLE `nongchanpin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ',
  `shangjia_id` int(11) NULL DEFAULT NULL COMMENT '商家',
  `nongchanpin_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '农产品名称  Search111 ',
  `nongchanpin_photo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '农产品照片',
  `nongchanpin_types` int(11) NULL DEFAULT NULL COMMENT '农产品类型 Search111',
  `nongchanpin_kucun_number` int(11) NULL DEFAULT NULL COMMENT '农产品库存',
  `nongchanpin_old_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '农产品原价 ',
  `nongchanpin_new_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '现价',
  `nongchanpin_clicknum` int(11) NULL DEFAULT NULL COMMENT '点击次数 ',
  `nongchanpin_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '农产品介绍 ',
  `shangxia_types` int(11) NULL DEFAULT NULL COMMENT '是否上架 ',
  `nongchanpin_delete` int(11) NULL DEFAULT NULL COMMENT '逻辑删除',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间  show1 show2 photoShow',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '农产品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of nongchanpin
-- ----------------------------
INSERT INTO `nongchanpin` VALUES (1, 3, '农产品名称1', 'upload/nongchanpin1.jpg', 1, 101, 100.00, 95.00, 4, '<p>农产品介绍1</p>', 1, 2, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (2, 2, '农产品名称2', '/upload/1750759218732.jpg', 3, 92, 67.73, 59.39, 417, '<p>农产品介绍2</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (3, 1, '农产品名称3', '/upload/1750759201367.jpg', 1, 98, 85.36, 86.44, 386, '<p>农产品介绍3</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (4, 1, '农产品名称4', 'upload/nongchanpin4.jpg', 4, 104, 514.64, 154.26, 225, '农产品介绍4', 1, 2, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (5, 2, '农产品名称5', '/upload/1750759183489.jpg', 3, 105, 45.00, 45.00, 40, '<p>农产品介绍5</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (6, 3, '农产品名称6', 'upload/nongchanpin6.jpg', 4, 106, 942.49, 468.07, 383, '农产品介绍6', 1, 2, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (7, 3, '农产品名称7', '/upload/1750759170451.jpg', 2, 107, 55.00, 48.00, 107, '<p>农产品介绍7</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (8, 2, '农产品名称8', '/upload/1750759156247.jpg', 4, 108, 12.00, 11.00, 264, '<p>农产品介绍8</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (9, 2, '农产品名称9', 'upload/nongchanpin9.jpg', 1, 109, 558.69, 71.73, 355, '农产品介绍9', 1, 2, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (10, 1, '农产品名称10', '/upload/1750759137673.jpg', 4, 1000, 54.54, 35.05, 241, '<p>农产品介绍10</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (11, 3, '农产品名称11', '/upload/1750759123874.jpg', 1, 1011, 32.79, 25.15, 191, '<p>农产品介绍11</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (12, 2, '农产品名称12', '/upload/1750759110291.jpg', 1, 1012, 10.00, 9.50, 274, '<p>农产品介绍12</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (13, 3, '农产品名称13', '/upload/1750759097936.jpg', 1, 1013, 2.05, 3.00, 472, '<p>农产品介绍13</p>', 1, 1, '2025-06-22 14:18:13');
INSERT INTO `nongchanpin` VALUES (14, 3, '农产品名称14', '/upload/1750759081719.jpg', 4, 1014, 1.25, 1.85, 414, '<p>农产品介绍14</p>', 1, 1, '2025-06-22 14:18:13');

-- ----------------------------
-- Table structure for nongchanpin_collection
-- ----------------------------
DROP TABLE IF EXISTS `nongchanpin_collection`;
CREATE TABLE `nongchanpin_collection`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nongchanpin_id` int(11) NULL DEFAULT NULL COMMENT '农产品',
  `yonghu_id` int(11) NULL DEFAULT NULL COMMENT '用户',
  `nongchanpin_collection_types` int(11) NULL DEFAULT NULL COMMENT '类型',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '收藏时间',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间 show3 photoShow',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '农产品收藏' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of nongchanpin_collection
-- ----------------------------
INSERT INTO `nongchanpin_collection` VALUES (15, 2, 4, 1, '2025-06-22 13:50:50', '2025-06-22 13:50:50');

-- ----------------------------
-- Table structure for nongchanpin_commentback
-- ----------------------------
DROP TABLE IF EXISTS `nongchanpin_commentback`;
CREATE TABLE `nongchanpin_commentback`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nongchanpin_id` int(11) NULL DEFAULT NULL COMMENT '农产品',
  `yonghu_id` int(11) NULL DEFAULT NULL COMMENT '用户',
  `nongchanpin_commentback_text` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '评价内容',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '评价时间',
  `reply_text` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '回复内容',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '回复时间',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '农产品评价' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of nongchanpin_commentback
-- ----------------------------
INSERT INTO `nongchanpin_commentback` VALUES (16, 3, 4, '好', '2025-06-22 13:50:13', NULL, NULL, '2025-06-22 13:50:13');
INSERT INTO `nongchanpin_commentback` VALUES (17, 10, 4, '不错', '2025-06-22 13:53:24', NULL, NULL, '2025-06-22 13:53:24');

-- ----------------------------
-- Table structure for nongchanpin_order
-- ----------------------------
DROP TABLE IF EXISTS `nongchanpin_order`;
CREATE TABLE `nongchanpin_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nongchanpin_order_uuid_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `address_id` int(11) NULL DEFAULT NULL COMMENT '收获地址 ',
  `nongchanpin_id` int(11) NULL DEFAULT NULL COMMENT '农产品',
  `yonghu_id` int(11) NULL DEFAULT NULL COMMENT '用户',
  `buy_number` int(11) NULL DEFAULT NULL COMMENT '购买数量',
  `nongchanpin_order_true_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '实付价格',
  `nongchanpin_order_courier_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `nongchanpin_order_courier_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单快递单号',
  `nongchanpin_order_types` int(11) NULL DEFAULT NULL COMMENT '订单类型',
  `insert_time` timestamp NULL DEFAULT NULL COMMENT '订单创建时间',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间 show3',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '农产品订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of nongchanpin_order
-- ----------------------------
INSERT INTO `nongchanpin_order` VALUES (5, '1682670109881', 3, 3, 1, 2, 172.88, '123', '3321', 105, '2025-06-20 16:21:50', '2025-06-20 16:21:50');
INSERT INTO `nongchanpin_order` VALUES (6, '1682670332670', 7, 3, 1, 1, 86.44, NULL, NULL, 102, '2025-06-20 16:25:33', '2025-06-20 16:25:33');
INSERT INTO `nongchanpin_order` VALUES (9, '1750571471185', 15, 2, 4, 10, 1193.90, NULL, NULL, 101, '2025-06-22 13:51:11', '2025-06-22 13:51:11');

-- ----------------------------
-- Table structure for shangjia
-- ----------------------------
DROP TABLE IF EXISTS `shangjia`;
CREATE TABLE `shangjia`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ',
  `username` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户 ',
  `password` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码 ',
  `shangjia_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商家名称 Search111 ',
  `shangjia_phone` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `shangjia_email` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `shangjia_photo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '营业执照展示 ',
  `shangjia_xingji_types` int(11) NULL DEFAULT NULL COMMENT '商家信用类型',
  `new_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '现有余额',
  `shangjia_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商家介绍 ',
  `shangjia_delete` int(11) NULL DEFAULT NULL COMMENT '逻辑删除',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间 show1 show2 photoShow ',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of shangjia
-- ----------------------------
INSERT INTO `shangjia` VALUES (1, 'a1', '123456', '商家名称1', '17703786901', '<EMAIL>', '/upload/1750758993785.jpg', 1, 4414.05, '<p>商家介绍1</p>', 1, '2025-06-20 16:06:15');
INSERT INTO `shangjia` VALUES (2, 'a2', '123456', '商家名称2', '17703786902', '<EMAIL>', '/upload/1750758974266.jpg', 1, 2052.93, '<p>商家介绍2</p>', 1, '2025-06-20 16:06:15');
INSERT INTO `shangjia` VALUES (3, 'a3', '123456', '商家名称3', '17703786903', '<EMAIL>', '/upload/1750758960006.jpg', 1, 568.56, '<p>商家介绍3</p>', 1, '2025-06-20 16:06:15');

-- ----------------------------
-- Table structure for token
-- ----------------------------
DROP TABLE IF EXISTS `token`;
CREATE TABLE `token`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userid` bigint(20) NOT NULL COMMENT '儿童id',
  `username` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '儿童名',
  `tablename` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `role` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色',
  `token` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `expiratedtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'token表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of token
-- ----------------------------
INSERT INTO `token` VALUES (1, 1, 'a1', 'yonghu', '用户', 'ssf5fq9mcn6ryf2vi0qd2g1igo8h1xsb', '2025-06-20 16:10:37', '2025-06-24 18:50:20');
INSERT INTO `token` VALUES (2, 1, 'admin', 'users', '管理员', 'yxty6dw6pgihfxhp8114y75m4227x3nt', '2025-06-19 16:15:44', '2025-06-24 18:55:43');
INSERT INTO `token` VALUES (3, 1, 'a1', 'shangjia', '商家', 'dpww3esh2ao8yyjrhrnoksytpbdlilhn', '2025-06-20 16:21:24', '2025-06-22 14:51:48');
INSERT INTO `token` VALUES (4, 4, 'zhangsan', 'yonghu', '用户', 'p7x6mazvaq9z078gdqj1pblp5scrbwi7', '2025-06-22 13:48:35', '2025-06-22 14:48:35');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '儿童名',
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `role` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '管理员' COMMENT '角色',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '管理员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', 'admin', '管理员', '2025-06-19 16:06:01');

-- ----------------------------
-- Table structure for yonghu
-- ----------------------------
DROP TABLE IF EXISTS `yonghu`;
CREATE TABLE `yonghu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户',
  `password` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `yonghu_uuid_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户编号 Search111 ',
  `yonghu_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户姓名 Search111 ',
  `yonghu_phone` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户手机号',
  `yonghu_id_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户身份证号',
  `yonghu_photo` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `sex_types` int(11) NULL DEFAULT NULL COMMENT '性别',
  `yonghu_email` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `new_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '余额 ',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yonghu
-- ----------------------------
INSERT INTO `yonghu` VALUES (1, 'lisi', '123456', '1682669175619', '李四', '17703786901', '410224199010102001', '/upload/1750759053586.jpg', 2, '<EMAIL>', 368.59, '2025-06-20 16:06:15');
INSERT INTO `yonghu` VALUES (2, 'xiaoxiong', '123456', '1682669175553', '小熊', '17703786902', '410224199010102002', '/upload/1750759042004.jpg', 1, '<EMAIL>', 85.44, '2025-06-21 16:06:15');
INSERT INTO `yonghu` VALUES (3, 'wangwu', '123456', '1682669175558', '王五', '17703786903', '410224199010102003', '/upload/1750759026289.jpg', 1, '<EMAIL>', 351.63, '2025-06-20 16:07:15');
INSERT INTO `yonghu` VALUES (4, 'zhangsan', '123456', '1750571306603', '张三', '13500000000', '50010319730829103X', '/upload/1750759014195.jpg', 2, '<EMAIL>', 105642.04, '2025-06-22 13:48:27');

SET FOREIGN_KEY_CHECKS = 1;
