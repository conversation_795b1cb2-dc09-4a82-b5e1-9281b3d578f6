<template>
    <div class="container">
        <div class="login-form">
            <h1 class="h1" style="color:#000;fontSize:28px;">农产品销售系统注册</h1>
            <el-form class="rgs-form">
                <el-form-item label="账号" class="input">
                    <el-input v-model="ruleForm.username" autocomplete="off" placeholder="账号"  />
                </el-form-item>
                <el-form-item label="密码" class="input">
                    <el-input type="password" v-model="ruleForm.password" autocomplete="off" show-password/>
                </el-form-item>
                <el-form-item label="重复密码" class="input">
                    <el-input type="passwo   rd" v-model="ruleForm.repetitionPassword" autocomplete="off" show-password/>
                </el-form-item>
                    <el-form-item label="商家名称" class="input" v-if="tableName=='shangjia'">
                        <el-input v-model="ruleForm.shangjiaName" autocomplete="off" placeholder="商家名称"  />
                    </el-form-item>
                    <el-form-item label="联系方式" class="input" v-if="tableName=='shangjia'">
                        <el-input v-model="ruleForm.shangjiaPhone" autocomplete="off" placeholder="联系方式"  />
                    </el-form-item>
                    <el-form-item label="邮箱" class="input" v-if="tableName=='shangjia'">
                        <el-input v-model="ruleForm.shangjiaEmail" autocomplete="off" placeholder="邮箱"  />
                    </el-form-item>
                    
                    <el-form-item label="用户姓名" class="input" v-if="tableName=='yonghu'">
                        <el-input v-model="ruleForm.yonghuName" autocomplete="off" placeholder="用户姓名"  />
                    </el-form-item>
                    <el-form-item label="用户手机号" class="input" v-if="tableName=='yonghu'">
                        <el-input v-model="ruleForm.yonghuPhone" autocomplete="off" placeholder="用户手机号"  />
                    </el-form-item>
                    <el-form-item label="用户身份证号" class="input" v-if="tableName=='yonghu'">
                        <el-input v-model="ruleForm.yonghuIdNumber" autocomplete="off" placeholder="用户身份证号"  />
                    </el-form-item>
                    
                    <el-form-item label="用户邮箱" class="input" v-if="tableName=='yonghu'">
                        <el-input v-model="ruleForm.yonghuEmail" autocomplete="off" placeholder="用户邮箱"  />
                    </el-form-item>
                    
                <div style="display: flex;flex-wrap: wrap;width: 100%;justify-content: center;">
                    <el-button class="btn" type="primary" @click="login()">注册</el-button>
                    <el-button class="btn close" type="primary" @click="close()">取消</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                ruleForm: {
                },
                tableName:"",
                rules: {},
                sexTypesOptions : [],
                shangjiaPhotoFlag:false,//用于刷新文件上传
                yonghuPhotoFlag:false,//用于刷新文件上传
            };
        },
        mounted(){
            let table = this.$storage.get("loginTable");
            this.tableName = table;

            //级联表的下拉框查询

        },
        methods: {
            // 获取uuid
            getUUID () {
                return new Date().getTime();
            },
            shangjiaPhotoUploadChange(fileUrls) {
               this.ruleForm.shangjiaPhoto = fileUrls;
                if(this.shangjiaPhotoFlag){
                    this.shangjiaPhotoFlag=false;
                }else{
                    this.shangjiaPhotoFlag=true;
                }
            },
            yonghuPhotoUploadChange(fileUrls) {
               this.ruleForm.yonghuPhoto = fileUrls;
                if(this.yonghuPhotoFlag){
                    this.yonghuPhotoFlag=false;
                }else{
                    this.yonghuPhotoFlag=true;
                }
            },
            close(){
                this.$router.push({ path: "/login" });
            },
            // 注册
            login() {

                            if((!this.ruleForm.username)){
                                this.$message.error('账号不能为空');
                                return
                            }
                            if((!this.ruleForm.password)){
                                this.$message.error('密码不能为空');
                                return
                            }
                            if((!this.ruleForm.repetitionPassword)){
                                this.$message.error('重复密码不能为空');
                                return
                            }
                            if(this.ruleForm.repetitionPassword != this.ruleForm.password){
                                this.$message.error('密码和重复密码不一致');
                                return
                            }
                            if((!this.ruleForm.shangjiaName)&& 'shangjia'==this.tableName){
                                this.$message.error('商家名称不能为空');
                                return
                            }
                             if('shangjia' == this.tableName && this.ruleForm.shangjiaPhone&&(!this.$validate.isMobile(this.ruleForm.shangjiaPhone))){
                                 this.$message.error('手机应输入手机格式');
                                 return
                             }
                            if('shangjia' == this.tableName && this.ruleForm.shangjiaEmail&&(!this.$validate.isEmail(this.ruleForm.shangjiaEmail))){
                                this.$message.error("邮箱应输入邮件格式");
                                return
                            }
                           
                            if((!this.ruleForm.yonghuName)&& 'yonghu'==this.tableName){
                                this.$message.error('用户姓名不能为空');
                                return
                            }
                             if('yonghu' == this.tableName && this.ruleForm.yonghuPhone&&(!this.$validate.isMobile(this.ruleForm.yonghuPhone))){
                                 this.$message.error('手机应输入手机格式');
                                 return
                             }
                            if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.tableName){
                                this.$message.error('用户身份证号不能为空');
                                return
                            }
                            
                            if('yonghu' == this.tableName && this.ruleForm.yonghuEmail&&(!this.$validate.isEmail(this.ruleForm.yonghuEmail))){
                                this.$message.error("邮箱应输入邮件格式");
                                return
                            }
                            
                this.$http({
                    url: `${this.tableName}/register`,
                    method: "post",
                    data:this.ruleForm
                }).then(({ data }) => {
                    if (data && data.code === 0) {
                    this.$message({
                        message: "注册成功,请登录后在个人中心页面补充个人数据",
                        type: "success",
                        duration: 1500,
                        onClose: () => {
                        this.$router.replace({ path: "/login" });
                }
                });
                } else {
                    this.$message.error(data.msg);
                }
            });
            }
        }
    };
</script>
<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		width: 100vw;
		max-width: 100%;
		position: relative;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
		background-image: url(/nongchanpinxiaoshou/upload/back-list-img-bg.jpg);
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;

		.login-form {
			width: 450px;
			padding: 40px;
			border-radius: 8px;
			background-color: #fff;
			box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
			position: relative;
			z-index: 10;

			.h1 {
				width: 100%;
				text-align: center;
				margin-bottom: 30px;
				color: #67a3ff;
				font-size: 26px;
				font-weight: bold;
			}

			.rgs-form {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				.el-form-item {
					width: 100%;
					display: flex;
					margin-bottom: 15px;

					& ::v-deep .el-form-item__content {
						flex: 1;
						display: flex;
					}
				}

				.input {
					width: 100%;
					margin-bottom: 15px;

					& ::v-deep .el-form-item__label {
						color: #333;
						font-size: 14px;
						padding-right: 15px;
					}

					& ::v-deep .el-input__inner {
						border: 1px solid #dcdfe6;
						border-radius: 4px;
						height: 40px;
						line-height: 40px;
						background-color: #f5f7fa;
						color: #333;
					}
				}

				.btn {
					margin: 10px;
					width: 120px;
					height: 40px;
					color: #fff;
					font-size: 14px;
					border: none;
					border-radius: 4px;
					background-color: #67a3ff;
				}

				.close {
					margin: 10px;
					width: 120px;
					height: 40px;
					color: #666;
					font-size: 14px;
					border: 1px solid #dcdfe6;
					border-radius: 4px;
					background-color: #fff;
				}
			}
		}
	}
</style>


