<template>
	<div class="navbar" :style="{background:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}">
		<div class="title-menu" :style="{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}">
			<el-image v-if="heads.headTitleImg" class="title-img" :style="{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}" :src="heads.headTitleImgUrl" fit="cover"></el-image>
			<div class="title-name" :style="{color:heads.headFontColor,fontSize:heads.headFontSize}">{{this.$project.projectName}}</div>
		</div>
		<div class="right-menu">
			<div class="user-info" :style="{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>
			<div class="logout" :style="{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}" @click="onLogout">退出登录</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				dialogVisible: false,
				ruleForm: {},
				user: {},
				heads: {
					"headLogoutFontHoverColor": "#ffffff",
					"headFontSize": "22px",
					"headUserInfoFontColor": "#ffffff",
					"headBoxShadow": "0 2px 8px rgba(0, 0, 0, 0.1)",
					"headTitleImgHeight": "44px",
					"headLogoutFontHoverBgColor": "rgba(0, 0, 0, 0.1)",
					"headFontColor": "#ffffff",
					"headTitleImg": false,
					"headHeight": "64px",
					"headTitleImgBorderRadius": "22px",
					"headTitleImgUrl": "http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg",
					"headBgColor": "linear-gradient(to right, #67a3ff, #94bcff)",
					"headTitleImgBoxShadow": "0 1px 6px #444",
					"headLogoutFontColor": "#ffffff",
					"headUserInfoFontSize": "16px",
					"headTitleImgWidth": "44px",
					"headTitleStyle": "1",
					"headLogoutFontSize": "16px"
				},
			};
		},
		created() {
			this.setHeaderStyle()
		},
		mounted() {
			let sessionTable = this.$storage.get("sessionTable")
			this.$http({
				url: sessionTable + '/session',
				method: "get"
			}).then(({
				data
			}) => {
				if (data && data.code === 0) {
					this.user = data.data;
					this.$storage.set('userid',data.data.id);
				} else {
					let message = this.$message
					message.error(data.msg);
				}
			});
		},
		methods: {
			onLogout() {
				let storage = this.$storage
				let router = this.$router
				storage.clear()
				router.replace({
					name: "login"
				});
			},
      			onIndexTap(){
      				window.location.href = `${this.$base.indexUrl}`
    			},
			setHeaderStyle() {
			  this.$nextTick(()=>{
			    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{
			      el.addEventListener("mouseenter", e => {
			        e.stopPropagation()
			        el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor
					el.style.color = this.heads.headLogoutFontHoverColor
			      })
			      el.addEventListener("mouseleave", e => {
			        e.stopPropagation()
			        el.style.backgroundColor = "transparent"
					el.style.color = this.heads.headLogoutFontColor
			      })
			    })
			  })
			},
		}
	};
</script>


<style lang="scss" scoped>
	.navbar {
		height: 64px;
		line-height: 64px;
		width: 100%;
		padding: 0 34px;
		box-sizing: border-box;
		position: relative;
		z-index: 111;
		
		.right-menu {
			position: absolute;
			right: 34px;
			top: 0;
			height: 100%;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			z-index: 111;
			
			.user-info {
				font-size: 16px;
				padding: 0 16px;
				color: #ffffff;
			}
			
			.logout {
				font-size: 16px;
				padding: 0 16px;
				cursor: pointer;
				border-radius: 20px;
				transition: all 0.3s;
				color: #ffffff;
				
				&:hover {
					background-color: rgba(0, 0, 0, 0.1);
				}
			}
		}
		
		.title-menu {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			width: 100%;
			height: 100%;
			
			.title-img {
				width: 44px;
				height: 44px;
				border-radius: 22px;
				box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
				margin-right: 16px;
			}
			
			.title-name {
				font-size: 24px;
				color: #ffffff;
				font-weight: 700;
				letter-spacing: 1px;
			}
		}
	}
</style>
