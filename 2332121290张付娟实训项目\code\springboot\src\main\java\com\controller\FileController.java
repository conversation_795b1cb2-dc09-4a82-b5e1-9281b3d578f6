package com.controller;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ResourceUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.annotation.IgnoreAuth;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.entity.ConfigEntity;
import com.entity.EIException;
import com.service.ConfigService;
import com.utils.R;

/**
 * 上传文件映射表
 */
@RestController
@RequestMapping("file")
@SuppressWarnings({"unchecked","rawtypes"})
public class FileController{
	@Autowired
    private ConfigService configService;
	/**
	 * 上传文件
	 */
	@RequestMapping("/upload")
	public R upload(@RequestParam("file") MultipartFile file, String type) throws Exception {
		if (file.isEmpty()) {
			throw new EIException("上传文件不能为空");
		}

		// 获取文件原始名称
		String originalFilename = file.getOriginalFilename();
		// 获取文件后缀
		String fileExt = originalFilename.substring(originalFilename.lastIndexOf("."));
		
		// 根据类型生成文件名前缀
		String prefix = "file";
		if (type != null && !type.isEmpty()) {
			// 根据type参数设置不同的前缀
			switch (type) {
				case "lunbo": 
					prefix = "lunbo"; 
					break;
				case "gonggao": 
					prefix = "gonggao"; 
					break;
				case "product": 
					prefix = "nongchanpin"; 
					break;
				case "shangjia": 
					prefix = "shangjia"; 
					break;
				case "yonghu": 
					prefix = "yonghu"; 
					break;
				case "1": 
					prefix = "avatar"; 
					break;
				default: 
					prefix = type; 
					break;
			}
		}
		
		// 查找当前前缀的最大序号
		int maxIndex = 0;
		File[] files = new File(new ClassPathResource("static/upload/").getFile().getAbsolutePath()).listFiles();
		if (files != null) {
			for (File existingFile : files) {
				String fileName = existingFile.getName();
				if (fileName.startsWith(prefix) && fileName.contains("_")) {
					try {
						String indexStr = fileName.substring(fileName.indexOf("_") + 1, fileName.lastIndexOf("."));
						int index = Integer.parseInt(indexStr);
						if (index > maxIndex) {
							maxIndex = index;
						}
					} catch (Exception e) {
						// 忽略格式不符的文件
					}
				}
			}
		}
		
		// 生成新的文件名，使用功能前缀+递增序号
		String newFileName = prefix + "_" + (maxIndex + 1) + fileExt.toLowerCase();

		// 获取编译目录下的静态资源目录 (target/classes/static/upload/)
		String targetStaticPath = new ClassPathResource("static/upload/").getFile().getAbsolutePath();
		// 确保目录存在
		File targetUploadDir = new File(targetStaticPath);
		if (!targetUploadDir.exists()) {
		    targetUploadDir.mkdirs();
		}

		// 创建文件对象并保存到编译目录
		File targetDest = new File(targetUploadDir, newFileName);
		// 保存文件到编译目录
		file.transferTo(targetDest);
		
		// 同时保存到源代码目录
		try {
		    // 获取项目根目录
		    String projectRoot = new File("").getAbsolutePath();
		    // 构建源代码中的静态资源目录路径
		    String srcStaticPath = projectRoot + "/src/main/resources/static/upload/";
		    // 确保源代码目录存在
		    File srcUploadDir = new File(srcStaticPath);
		    if (!srcUploadDir.exists()) {
		        srcUploadDir.mkdirs();
		    }
		    
		    // 创建源代码目录中的文件对象
		    File srcDest = new File(srcUploadDir, newFileName);
		    // 将文件从编译目录复制到源代码目录
		    FileUtils.copyFile(targetDest, srcDest);
		} catch (Exception e) {
		    // 记录异常但不影响主要功能
		    System.err.println("备份文件到源码目录失败: " + e.getMessage());
		    e.printStackTrace();
		}

		// 文件访问路径，确保访问路径正确 - 只返回相对路径，由前端负责处理完整URL
		String fileUrl = "/upload/" + newFileName;

		// 处理特殊类型（如头像）
		if (StringUtils.isNotBlank(type) && type.equals("1")) {
		    ConfigEntity configEntity = configService.selectOne(new EntityWrapper<ConfigEntity>().eq("name", "faceFile"));
		    if (configEntity == null) {
		        configEntity = new ConfigEntity();
		        configEntity.setName("faceFile");
		        configEntity.setValue(fileUrl);
		    } else {
		        configEntity.setValue(fileUrl);
		    }
		    configService.insertOrUpdate(configEntity);
		}

		// 返回文件访问路径，确保前端可以正确拼接
		return R.ok().put("file", fileUrl);
	}
	
	/**
	 * 下载文件
	 */
	@IgnoreAuth
	@RequestMapping("/download")
	public ResponseEntity<byte[]> download(@RequestParam String fileName) {
		try {
			// 获取静态资源目录
			String staticPath = new ClassPathResource("static/upload/").getFile().getAbsolutePath();
			File file = new File(staticPath, fileName);
			if(file.exists()){
				HttpHeaders headers = new HttpHeaders();
			    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);    
			    headers.setContentDispositionFormData("attachment", fileName);    
			    return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(file),headers, HttpStatus.CREATED);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return new ResponseEntity<byte[]>(HttpStatus.INTERNAL_SERVER_ERROR);
	}
	
}
