!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=11),domSupported:"undefined"!=typeof document}}function i(t,e){"createCanvas"===t&&(ap=null),np[t]=e}function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=Kf.call(t);if("[object Array]"===i){if(!B(t)){e=[];for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}else if(Zf[i]){if(!B(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}}else if(!qf[i]&&!B(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function r(t,e,i){if(!S(e)||!S(t))return i?n(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!S(s)||!S(o)||_(s)||_(o)||T(s)||T(o)||M(s)||M(o)||B(s)||B(o)?!i&&a in t||(t[a]=n(e[a],!0)):r(o,s,i)}return t}function a(t,e){for(var i=t[0],n=1,a=t.length;a>n;n++)i=r(i,t[n],e);return i}function o(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function s(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function l(){return ap||(ap=rp().getContext("2d")),ap}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function u(t,e){function i(){}var n=t.prototype;i.prototype=e.prototype,t.prototype=new i;for(var r in n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function c(t,e,i){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,i)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,i){if(t&&e)if(t.forEach&&t.forEach===Qf)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function p(t,e,i){if(t&&e){if(t.map&&t.map===ep)return t.map(e,i);for(var n=[],r=0,a=t.length;a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function g(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===ip)return t.reduce(e,i,n);for(var r=0,a=t.length;a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function v(t,e,i){if(t&&e){if(t.filter&&t.filter===Jf)return t.filter(e,i);for(var n=[],r=0,a=t.length;a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e,i){if(t&&e)for(var n=0,r=t.length;r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function y(t,e){var i=tp.call(arguments,2);return function(){return t.apply(e,i.concat(tp.call(arguments)))}}function x(t){var e=tp.call(arguments,1);return function(){return t.apply(this,e.concat(tp.call(arguments)))}}function _(t){return"[object Array]"===Kf.call(t)}function w(t){return"function"==typeof t}function b(t){return"[object String]"===Kf.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function M(t){return!!qf[Kf.call(t)]}function I(t){return!!Zf[Kf.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function C(t){return t!==t}function D(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function A(t,e){return null!=t?t:e}function k(t,e,i){return null!=t?t:null!=e?e:i}function P(){return Function.call.apply(tp,arguments)}function L(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function O(t,e){if(!t)throw new Error(e)}function E(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function z(t){t[op]=!0}function B(t){return t[op]}function R(t){function e(t,e){i?n.set(t,e):n.set(e,t)}var i=_(t);this.data={};var n=this;t instanceof R?t.each(e):t&&f(t,e)}function N(t){return new R(t)}function F(t,e){for(var i=new t.constructor(t.length+e.length),n=0;n<t.length;n++)i[n]=t[n];var r=t.length;for(n=0;n<e.length;n++)i[n+r]=e[n];return i}function V(){}function H(t,e){var i=new lp(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function W(t,e){return t[0]=e[0],t[1]=e[1],t}function G(t){var e=new lp(2);return e[0]=t[0],e[1]=t[1],e}function X(t,e,i){return t[0]=e,t[1]=i,t}function Y(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function U(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t}function j(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function q(t){return Math.sqrt(Z(t))}function Z(t){return t[0]*t[0]+t[1]*t[1]}function K(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t}function $(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t}function Q(t,e){return t[0]*e[0]+t[1]*e[1]}function J(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function te(t,e){var i=q(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function ee(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function ie(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function ne(t,e){return t[0]=-e[0],t[1]=-e[1],t}function re(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t}function ae(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function oe(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function se(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}function le(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function he(t,e){return{target:t,topTarget:e&&e.topTarget}}function ue(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}function ce(t,e,i,n,r,a){var o=t._$handlers;if("function"==typeof i&&(r=n,n=i,i=null),!n||!e)return t;i=ue(t,i),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===n)return t;var l={h:n,one:a,query:i,ctx:r||t,callAtLast:n.zrEventfulCallAtLast},h=o[e].length-1,u=o[e][h];return u&&u.callAtLast?o[e].splice(h,0,l):o[e].push(l),t}function de(t,e,i,n,r,a){var o=n+"-"+r,s=t.length;if(a.hasOwnProperty(o))return a[o];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/vp);return t[i][l]}for(var h=n|1<<i,u=i+1;n&1<<u;)u++;for(var c=0,d=0,f=0;s>d;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[i][d]*de(t,e-1,u,h,r|p,a),f++)}return a[o]=c,c}function fe(t,e){var i=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],n={},r=de(i,8,0,0,0,n);if(0!==r){for(var a=[],o=0;8>o;o++)for(var s=0;8>s;s++)null==a[s]&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*de(i,7,0===o?1:0,1<<o,1<<s,n)/r*e[o];return function(t,e,i){var n=e*a[6]+i*a[7]+1;t[0]=(e*a[0]+i*a[1]+a[2])/n,t[1]=(e*a[3]+i*a[4]+a[5])/n}}}function pe(t,e,i,n){return i=i||{},n||!jf.canvasSupported?ge(t,e,i):jf.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):ge(t,e,i),i}function ge(t,e,i){if(t.getBoundingClientRect&&jf.domSupported){var n=e.clientX,r=e.clientY;if("CANVAS"===t.nodeName.toUpperCase()){var a=t.getBoundingClientRect();return i.zrX=n-a.left,void(i.zrY=r-a.top)}var o=t[xp]||(t[xp]={}),s=me(ve(t,o),o);if(s)return s(_p,n,r),i.zrX=_p[0],void(i.zrY=_p[1])}i.zrX=i.zrY=0}function ve(t,e){var i=e.markers;if(i)return i;i=e.markers=[];for(var n=["left","right"],r=["top","bottom"],a=0;4>a;a++){var o=document.createElement("div"),s=o.style,l=a%2,h=(a>>1)%2;s.cssText=["position:absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","width:0","height:0",n[l]+":0",r[h]+":0",n[1-l]+":auto",r[1-h]+":auto",""].join("!important;"),t.appendChild(o),i.push(o)}return i}function me(t,e){for(var i=e.transformer,n=e.srcCoords,r=!0,a=[],o=[],s=0;4>s;s++){var l=t[s].getBoundingClientRect(),h=2*s,u=l.left,c=l.top;a.push(u,c),r&=n&&u===n[h]&&c===n[h+1],o.push(t[s].offsetLeft,t[s].offsetTop)}return r?i:(e.srcCoords=a,e.transformer=fe(a,o))}function ye(t){return t||window.event}function xe(t,e,i){if(e=ye(e),null!=e.zrX)return e;var n=e.type,r=n&&n.indexOf("touch")>=0;if(r){var a="touchend"!==n?e.targetTouches[0]:e.changedTouches[0];a&&pe(t,a,e,i)}else pe(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&yp.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function _e(t,e,i,n){mp?t.addEventListener(e,i,n):t.attachEvent("on"+e,i)}function we(t,e,i,n){mp?t.removeEventListener(e,i,n):t.detachEvent("on"+e,i)}function be(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function Se(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function Me(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:Ie}}function Ie(){wp(this.event)}function Te(){}function Ce(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return n?Mp:!0}return!1}function De(t,e,i){var n=t.painter;return 0>e||e>n.getWidth()||0>i||i>n.getHeight()}function Ae(){var t=new Cp(6);return ke(t),t}function ke(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Pe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Le(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function Oe(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function Ee(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(i),u=Math.cos(i);return t[0]=n*u+o*h,t[1]=-n*h+o*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t}function ze(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function Be(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}function Re(t){var e=Ae();return Pe(e,t),e}function Ne(t){return t>kp||-kp>t}function Fe(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Ve(t){return t=Math.round(t),0>t?0:t>255?255:t}function He(t){return t=Math.round(t),0>t?0:t>360?360:t}function We(t){return 0>t?0:t>1?1:t}function Ge(t){return Ve(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function Xe(t){return We(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function Ye(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function Ue(t,e,i){return t+(e-t)*i}function je(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function qe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Ze(t,e){Xp&&qe(Xp,e),Xp=Gp.put(t,Xp||e.slice())}function Ke(t,e){if(t){e=e||[];var i=Gp.get(t);if(i)return qe(e,i);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in Wp)return qe(e,Wp[n]),Ze(t,e),e;if("#"!==n.charAt(0)){var r=n.indexOf("("),a=n.indexOf(")");if(-1!==r&&a+1===n.length){var o=n.substr(0,r),s=n.substr(r+1,a-(r+1)).split(","),l=1;switch(o){case"rgba":if(4!==s.length)return void je(e,0,0,0,1);l=Xe(s.pop());case"rgb":return 3!==s.length?void je(e,0,0,0,1):(je(e,Ge(s[0]),Ge(s[1]),Ge(s[2]),l),Ze(t,e),e);case"hsla":return 4!==s.length?void je(e,0,0,0,1):(s[3]=Xe(s[3]),$e(s,e),Ze(t,e),e);case"hsl":return 3!==s.length?void je(e,0,0,0,1):($e(s,e),Ze(t,e),e);default:return}}je(e,0,0,0,1)}else{if(4===n.length){var h=parseInt(n.substr(1),16);return h>=0&&4095>=h?(je(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),Ze(t,e),e):void je(e,0,0,0,1)}if(7===n.length){var h=parseInt(n.substr(1),16);return h>=0&&16777215>=h?(je(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),Ze(t,e),e):void je(e,0,0,0,1)}}}}function $e(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=Xe(t[1]),r=Xe(t[2]),a=.5>=r?r*(n+1):r+n-r*n,o=2*r-a;return e=e||[],je(e,Ve(255*Ye(o,a,i+1/3)),Ve(255*Ye(o,a,i)),Ve(255*Ye(o,a,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Qe(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,h=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>h?l/(s+o):l/(2-s-o);var u=((s-n)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;n===s?e=d-c:r===s?e=1/3+u-d:a===s&&(e=2/3+c-u),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,i,h];return null!=t[3]&&f.push(t[3]),f}}function Je(t,e){var i=Ke(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0,i[n]>255?i[n]=255:t[n]<0&&(i[n]=0);return ai(i,4===i.length?"rgba":"rgb")}}function ti(t){var e=Ke(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function ei(t,e,i){if(e&&e.length&&t>=0&&1>=t){i=i||[];var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=e[r],s=e[a],l=n-r;return i[0]=Ve(Ue(o[0],s[0],l)),i[1]=Ve(Ue(o[1],s[1],l)),i[2]=Ve(Ue(o[2],s[2],l)),i[3]=We(Ue(o[3],s[3],l)),i}}function ii(t,e,i){if(e&&e.length&&t>=0&&1>=t){var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=Ke(e[r]),s=Ke(e[a]),l=n-r,h=ai([Ve(Ue(o[0],s[0],l)),Ve(Ue(o[1],s[1],l)),Ve(Ue(o[2],s[2],l)),We(Ue(o[3],s[3],l))],"rgba");return i?{color:h,leftIndex:r,rightIndex:a,value:n}:h}}function ni(t,e,i,n){return t=Ke(t),t?(t=Qe(t),null!=e&&(t[0]=He(e)),null!=i&&(t[1]=Xe(i)),null!=n&&(t[2]=Xe(n)),ai($e(t),"rgba")):void 0}function ri(t,e){return t=Ke(t),t&&null!=e?(t[3]=We(e),ai(t,"rgba")):void 0}function ai(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}}function oi(t,e){return t[e]}function si(t,e,i){t[e]=i}function li(t,e,i){return(e-t)*i+t}function hi(t,e,i){return i>.5?e:t}function ui(t,e,i,n,r){var a=t.length;if(1===r)for(var o=0;a>o;o++)n[o]=li(t[o],e[o],i);else for(var s=a&&t[0].length,o=0;a>o;o++)for(var l=0;s>l;l++)n[o][l]=li(t[o][l],e[o][l],i)}function ci(t,e,i){var n=t.length,r=e.length;if(n!==r){var a=n>r;if(a)t.length=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:qp.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function di(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function fi(t,e,i,n,r,a,o,s,l){var h=t.length;if(1===l)for(var u=0;h>u;u++)s[u]=pi(t[u],e[u],i[u],n[u],r,a,o);else for(var c=t[0].length,u=0;h>u;u++)for(var d=0;c>d;d++)s[u][d]=pi(t[u][d],e[u][d],i[u][d],n[u][d],r,a,o)}function pi(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function gi(t){if(d(t)){var e=t.length;if(d(t[0])){for(var i=[],n=0;e>n;n++)i.push(qp.call(t[n]));return i}return qp.call(t)}return t}function vi(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function mi(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function yi(t,e,i,n,r,a){var o=t._getter,s=t._setter,l="spline"===e,h=n.length;if(h){var u,c=n[0].value,f=d(c),p=!1,g=!1,v=f?mi(n):0;n.sort(function(t,e){return t.time-e.time}),u=n[h-1].time;for(var m=[],y=[],x=n[0].value,_=!0,w=0;h>w;w++){m.push(n[w].time/u);var b=n[w].value;if(f&&di(b,x,v)||!f&&b===x||(_=!1),x=b,"string"==typeof b){var S=Ke(b);S?(b=S,p=!0):g=!0}y.push(b)}if(a||!_){for(var M=y[h-1],w=0;h-1>w;w++)f?ci(y[w],M,v):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&ci(o(t._target,r),M,v);var I,T,C,D,A,k,P=0,L=0;if(p)var O=[0,0,0,0];var E=function(t,e){var i;if(0>e)i=0;else if(L>e){for(I=Math.min(P+1,h-1),i=I;i>=0&&!(m[i]<=e);i--);i=Math.min(i,h-2)}else{for(i=P;h>i&&!(m[i]>e);i++);i=Math.min(i-1,h-2)}P=i,L=e;var n=m[i+1]-m[i];if(0!==n)if(T=(e-m[i])/n,l)if(D=y[i],C=y[0===i?i:i-1],A=y[i>h-2?h-1:i+1],k=y[i>h-3?h-1:i+2],f)fi(C,D,A,k,T,T*T,T*T*T,o(t,r),v);else{var a;if(p)a=fi(C,D,A,k,T,T*T,T*T*T,O,1),a=vi(O);else{if(g)return hi(D,A,T);a=pi(C,D,A,k,T,T*T,T*T*T)}s(t,r,a)}else if(f)ui(y[i],y[i+1],T,o(t,r),v);else{var a;if(p)ui(y[i],y[i+1],T,O,1),a=vi(O);else{if(g)return hi(y[i],y[i+1],T);a=li(y[i],y[i+1],T)}s(t,r,a)}},z=new Fe({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:E,ondestroy:i});return e&&"spline"!==e&&(z.easing=e),z}}}function xi(t,e,i,n,r,a,o,s){function l(){u--,u||a&&a()}b(n)?(a=r,r=n,n=0):w(r)?(a=r,r="linear",n=0):w(n)?(a=n,n=0):w(i)?(a=i,i=500):i||(i=500),t.stopAnimation(),_i(t,"",t,e,i,n,s);var h=t.animators.slice(),u=h.length;u||a&&a();for(var c=0;c<h.length;c++)h[c].done(l).start(r,o)}function _i(t,e,i,n,r,a,o){var s={},l=0;for(var h in n)n.hasOwnProperty(h)&&(null!=i[h]?S(n[h])&&!d(n[h])?_i(t,e?e+"."+h:h,i[h],n[h],r,a,o):(o?(s[h]=i[h],wi(t,e,h,n[h])):s[h]=n[h],l++):null==n[h]||o||wi(t,e,h,n[h]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function wi(t,e,i,n){if(e){var r={};r[e]={},r[e][i]=n,t.attr(r)}else t.attr(i,n)}function bi(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}function Si(t){for(var e=0;t>=sg;)e|=1&t,t>>=1;return t+e}function Mi(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;i>r&&n(t[r],t[r-1])<0;)r++;Ii(t,e,r)}else for(;i>r&&n(t[r],t[r-1])>=0;)r++;return r-e}function Ii(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function Ti(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var h=n-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=o}}function Ci(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])>0?o=u+1:l=u}return l}function Di(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])<0?l=u:o=u+1}return l}function Ai(t,e){function i(t,e){l[c]=t,h[c]=e,c+=1}function n(){for(;c>1;){var t=c-2;if(t>=1&&h[t-1]<=h[t]+h[t+1]||t>=2&&h[t-2]<=h[t]+h[t-1])h[t-1]<h[t+1]&&t--;else if(h[t]>h[t+1])break;a(t)}}function r(){for(;c>1;){var t=c-2;t>0&&h[t-1]<h[t+1]&&t--,a(t)}}function a(i){var n=l[i],r=h[i],a=l[i+1],u=h[i+1];h[i]=r+u,i===c-3&&(l[i+1]=l[i+2],h[i+1]=h[i+2]),c--;var d=Di(t[a],t,n,r,0,e);n+=d,r-=d,0!==r&&(u=Ci(t[n+r-1],t,a,u,u-1,e),0!==u&&(u>=r?o(n,r,a,u):s(n,r,a,u)))}function o(i,n,r,a){var o=0;for(o=0;n>o;o++)d[o]=t[i+o];var s=0,l=r,h=i;if(t[h++]=t[l++],0!==--a){if(1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];return void(t[h+a]=d[s])}for(var c,f,p,g=u;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[h++]=t[l++],f++,c=0,0===--a){p=!0;break}}else if(t[h++]=d[s++],c++,f=0,1===--n){p=!0;break}while(g>(c|f));if(p)break;do{if(c=Di(t[l],d,s,n,0,e),0!==c){for(o=0;c>o;o++)t[h+o]=d[s+o];if(h+=c,s+=c,n-=c,1>=n){p=!0;break}}if(t[h++]=t[l++],0===--a){p=!0;break}if(f=Ci(d[s],t,l,a,0,e),0!==f){for(o=0;f>o;o++)t[h+o]=t[l+o];if(h+=f,l+=f,a-=f,0===a){p=!0;break}}if(t[h++]=d[s++],1===--n){p=!0;break}g--}while(c>=lg||f>=lg);if(p)break;0>g&&(g=0),g+=2}if(u=g,1>u&&(u=1),1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];t[h+a]=d[s]}else{if(0===n)throw new Error;for(o=0;n>o;o++)t[h+o]=d[s+o]}}else for(o=0;n>o;o++)t[h+o]=d[s+o]}function s(i,n,r,a){var o=0;for(o=0;a>o;o++)d[o]=t[r+o];var s=i+n-1,l=a-1,h=r+a-1,c=0,f=0;if(t[h--]=t[s--],0!==--n){if(1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];return void(t[h]=d[l])}for(var p=u;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[h--]=t[s--],g++,v=0,0===--n){m=!0;break}}else if(t[h--]=d[l--],v++,g=0,1===--a){m=!0;break}while(p>(g|v));if(m)break;do{if(g=n-Di(d[l],t,i,n,n-1,e),0!==g){for(h-=g,s-=g,n-=g,f=h+1,c=s+1,o=g-1;o>=0;o--)t[f+o]=t[c+o];if(0===n){m=!0;break}}if(t[h--]=d[l--],1===--a){m=!0;break}if(v=a-Ci(t[s],d,0,a,a-1,e),0!==v){for(h-=v,l-=v,a-=v,f=h+1,c=l+1,o=0;v>o;o++)t[f+o]=d[c+o];if(1>=a){m=!0;break}}if(t[h--]=t[s--],0===--n){m=!0;break}p--}while(g>=lg||v>=lg);if(m)break;0>p&&(p=0),p+=2}if(u=p,1>u&&(u=1),1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];t[h]=d[l]}else{if(0===a)throw new Error;for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}}else for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}var l,h,u=lg,c=0,d=[];l=[],h=[],this.mergeRuns=n,this.forceMergeRuns=r,this.pushRun=i}function ki(t,e,i,n){i||(i=0),n||(n=t.length);var r=n-i;if(!(2>r)){var a=0;if(sg>r)return a=Mi(t,i,n,e),void Ti(t,i,n,i+a,e);var o=new Ai(t,e),s=Si(r);do{if(a=Mi(t,i,n,e),s>a){var l=r;l>s&&(l=s),Ti(t,i,i+l,i+a,e),a=l}o.pushRun(i,a),o.mergeRuns(),r-=a,i+=a}while(0!==r);o.forceMergeRuns()}}function Pi(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Li(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i.height+i.y,o=o*i.height+i.y),n=isNaN(n)?0:n,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(n,a,r,o);return s}function Oi(t,e,i){var n=i.width,r=i.height,a=Math.min(n,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var h=t.createRadialGradient(o,s,0,o,s,l);return h}function Ei(){return!1}function zi(t,e,i){var n=rp(),r=e.getWidth(),a=e.getHeight(),o=n.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=r+"px",o.height=a+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=a*i,n}function Bi(t){if("string"==typeof t){var e=bg.get(t);return e&&e.image}return t}function Ri(t,e,i,n,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!i)return e;var a=bg.get(t),o={hostEl:i,cb:n,cbPayload:r};return a?(e=a.image,!Fi(e)&&a.pending.push(o)):(e=new Image,e.onload=e.onerror=Ni,bg.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Ni(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function Fi(t){return t&&t.width&&t.height}function Vi(t,e){Dg[t]=e}function Hi(t,e){e=e||Cg;var i=t+":"+e;if(Sg[i])return Sg[i];for(var n=(t+"").split("\n"),r=0,a=0,o=n.length;o>a;a++)r=Math.max(Ji(n[a],e).width,r);return Mg>Ig&&(Mg=0,Sg={}),Mg++,Sg[i]=r,r}function Wi(t,e,i,n,r,a,o,s){return o?Xi(t,e,i,n,r,a,o,s):Gi(t,e,i,n,r,a,s)}function Gi(t,e,i,n,r,a,o){var s=tn(t,e,r,a,o),l=Hi(t,e);r&&(l+=r[1]+r[3]);var h=s.outerHeight,u=Yi(0,l,i),c=Ui(0,h,n),d=new bi(u,c,l,h);return d.lineHeight=s.lineHeight,d}function Xi(t,e,i,n,r,a,o,s){var l=en(t,{rich:o,truncate:s,font:e,textAlign:i,textPadding:r,textLineHeight:a}),h=l.outerWidth,u=l.outerHeight,c=Yi(0,h,i),d=Ui(0,u,n);return new bi(c,d,h,u)}function Yi(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function Ui(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function ji(t,e,i){var n=e.textPosition,r=e.textDistance,a=i.x,o=i.y;r=r||0;var s=i.height,l=i.width,h=s/2,u="left",c="top";switch(n){case"left":a-=r,o+=h,u="right",c="middle";break;case"right":a+=r+l,o+=h,c="middle";break;case"top":a+=l/2,o-=r,u="center",c="bottom";break;case"bottom":a+=l/2,o+=s+r,u="center";break;case"inside":a+=l/2,o+=h,u="center",c="middle";break;case"insideLeft":a+=r,o+=h,c="middle";break;case"insideRight":a+=l-r,o+=h,u="right",c="middle";break;case"insideTop":a+=l/2,o+=r,u="center";break;case"insideBottom":a+=l/2,o+=s-r,u="center",c="bottom";break;case"insideTopLeft":a+=r,o+=r;break;case"insideTopRight":a+=l-r,o+=r,u="right";break;case"insideBottomLeft":a+=r,o+=s-r,c="bottom";break;case"insideBottomRight":a+=l-r,o+=s-r,u="right",c="bottom"}return t=t||{},t.x=a,t.y=o,t.textAlign=u,t.textVerticalAlign=c,t}function qi(t,e,i,n,r){if(!e)return"";var a=(t+"").split("\n");r=Zi(e,i,n,r);for(var o=0,s=a.length;s>o;o++)a[o]=Ki(a[o],r);return a.join("\n")}function Zi(t,e,i,n){n=o({},n),n.font=e;var i=A(i,"...");n.maxIterations=A(n.maxIterations,2);var r=n.minChar=A(n.minChar,0);n.cnCharWidth=Hi("国",e);var a=n.ascCharWidth=Hi("a",e);n.placeholder=A(n.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var h=Hi(i,e);return h>s&&(i="",h=0),s=t-h,n.ellipsis=i,n.ellipsisWidth=h,n.contentWidth=s,n.containerWidth=t,n}function Ki(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var a=Hi(t,n);if(i>=a)return t;for(var o=0;;o++){if(r>=a||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?$i(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=Hi(t,n)}return""===t&&(t=e.placeholder),t}function $i(t,e,i,n){for(var r=0,a=0,o=t.length;o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}function Qi(t){return Hi("国",t)}function Ji(t,e){return Dg.measureText(t,e)}function tn(t,e,i,n,r){null!=t&&(t+="");var a=A(n,Qi(e)),o=t?t.split("\n"):[],s=o.length*a,l=s,h=!0;if(i&&(l+=i[0]+i[2]),t&&r){h=!1;var u=r.outerHeight,c=r.outerWidth;if(null!=u&&l>u)t="",o=[];else if(null!=c)for(var d=Zi(c-(i?i[1]+i[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=o.length;p>f;f++)o[f]=Ki(o[f],d)}return{lines:o,height:s,outerHeight:l,lineHeight:a,canCacheByTextString:h}}function en(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=Tg.lastIndex=0;null!=(n=Tg.exec(t));){var a=n.index;a>r&&nn(i,t.substring(r,a)),nn(i,n[2],n[1]),r=Tg.lastIndex}r<t.length&&nn(i,t.substring(r,t.length));var o=i.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,m=0,y=0;y<g.tokens.length;y++){var x=g.tokens[y],_=x.styleName&&e.rich[x.styleName]||{},w=x.textPadding=_.textPadding,b=x.font=_.font||e.font,S=x.textHeight=A(_.textHeight,Qi(b));if(w&&(S+=w[0]+w[2]),x.height=S,x.lineHeight=k(_.textLineHeight,e.textLineHeight,S),x.textAlign=_&&_.textAlign||e.textAlign,x.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+x.lineHeight>f)return{lines:[],width:0,height:0};x.textWidth=Hi(x.text,b);var M=_.textWidth,I=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))x.percentWidth=M,h.push(x),M=0;else{if(I){M=x.textWidth;var T=_.textBackgroundColor,C=T&&T.image;C&&(C=Bi(C),Fi(C)&&(M=Math.max(M,C.width*S/C.height)))}var D=w?w[1]+w[3]:0;M+=D;var P=null!=d?d-m:null;null!=P&&M>P&&(!I||D>P?(x.text="",x.textWidth=M=0):(x.text=qi(x.text,P-D,b,c.ellipsis,{minChar:c.minChar}),x.textWidth=Hi(x.text,b),M=x.textWidth+D))}m+=x.width=M,_&&(v=Math.max(v,x.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}i.outerWidth=i.width=A(e.textWidth,l),i.outerHeight=i.height=A(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(var p=0;p<h.length;p++){var x=h[p],L=x.percentWidth;x.width=parseInt(L,10)/100*l}return i}function nn(t,e,i){for(var n=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:i,text:s,isLineHolder:!s&&!n};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function rn(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&E(e)||t.textFont||t.font}function an(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;0>l&&(o+=l,l=-l),0>h&&(s+=h,h=-h),"number"==typeof u?i=n=r=a=u:u instanceof Array?1===u.length?i=n=r=a=u[0]:2===u.length?(i=r=u[0],n=a=u[1]):3===u.length?(i=u[0],n=a=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],a=u[3]):i=n=r=a=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),n+r>h&&(c=n+r,n*=h/c,r*=h/c),i+a>h&&(c=i+a,i*=h/c,a*=h/c),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.arc(o+l-n,s+n,n,-Math.PI/2,0),t.lineTo(o+l,s+h-r),0!==r&&t.arc(o+l-r,s+h-r,r,0,Math.PI/2),t.lineTo(o+a,s+h),0!==a&&t.arc(o+a,s+h-a,a,Math.PI/2,Math.PI),t.lineTo(o,s+i),0!==i&&t.arc(o+i,s+i,i,Math.PI,1.5*Math.PI)}function on(t){return sn(t),f(t.rich,sn),t}function sn(t){if(t){t.font=rn(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||kg[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||Pg[i]?i:"top";var n=t.textPadding;n&&(t.textPadding=L(t.textPadding))}}function ln(t,e,i,n,r,a){n.rich?un(t,e,i,n,r,a):hn(t,e,i,n,r,a)}function hn(t,e,i,n,r,a){var o,s=pn(n),l=!1,h=e.__attrCachedBy===dg.PLAIN_TEXT;a!==fg?(a&&(o=a.style,l=!s&&h&&o),e.__attrCachedBy=s?dg.NONE:dg.PLAIN_TEXT):h&&(e.__attrCachedBy=dg.NONE);var u=n.font||Ag;l&&u===(o.font||Ag)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var d=n.textPadding,f=n.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=tn(i,c,d,f,n.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=mn(Eg,t,n,r),x=y.baseX,_=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;dn(e,n,r,x,_);var S=Ui(_,g,b),M=x,I=S;if(s||d){var T=Hi(i,c),C=T;d&&(C+=d[1]+d[3]);var D=Yi(x,C,w);s&&gn(t,e,n,D,S,C,g),d&&(M=bn(x,w,d),I+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=n.opacity||1;for(var A=0;A<Lg.length;A++){var k=Lg[A],P=k[0],L=k[1],O=n[P];l&&O===o[P]||(e[L]=cg(e,L,O||k[2]))}I+=m/2;var E=n.textStrokeWidth,z=l?o.textStrokeWidth:null,B=!l||E!==z,R=!l||B||n.textStroke!==o.textStroke,N=xn(n.textStroke,E),F=_n(n.textFill);if(N&&(B&&(e.lineWidth=E),R&&(e.strokeStyle=N)),F&&(l&&n.textFill===o.textFill||(e.fillStyle=F)),1===v.length)N&&e.strokeText(v[0],M,I),F&&e.fillText(v[0],M,I);else for(var A=0;A<v.length;A++)N&&e.strokeText(v[A],M,I),F&&e.fillText(v[A],M,I),I+=m}function un(t,e,i,n,r,a){a!==fg&&(e.__attrCachedBy=dg.NONE);var o=t.__textCotentBlock;(!o||t.__dirtyText)&&(o=t.__textCotentBlock=en(i,n)),cn(t,e,o,n,r)}function cn(t,e,i,n,r){var a=i.width,o=i.outerWidth,s=i.outerHeight,l=n.textPadding,h=mn(Eg,t,n,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;dn(e,n,r,u,c);var p=Yi(u,o,d),g=Ui(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+a;pn(n)&&gn(t,e,n,p,g,o,s);for(var x=0;x<i.lines.length;x++){for(var _,w=i.lines[x],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,T=0,C=v,D=y,A=S-1;S>T&&(_=b[T],!_.textAlign||"left"===_.textAlign);)fn(t,e,_,n,M,m,C,"left"),I-=_.width,C+=_.width,T++;for(;A>=0&&(_=b[A],"right"===_.textAlign);)fn(t,e,_,n,M,m,D,"right"),I-=_.width,D-=_.width,A--;for(C+=(a-(C-v)-(y-D)-I)/2;A>=T;)_=b[T],fn(t,e,_,n,M,m,C+_.width/2,"center"),C+=_.width,T++;m+=M}}function dn(t,e,i,n,r){if(i&&e.textRotation){var a=e.textOrigin;"center"===a?(n=i.width/2+i.x,r=i.height/2+i.y):a&&(n=a[0]+i.x,r=a[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function fn(t,e,i,n,r,a,o,s){var l=n.rich[i.styleName]||{};l.text=i.text;var h=i.textVerticalAlign,u=a+r/2;"top"===h?u=a+i.height/2:"bottom"===h&&(u=a+r-i.height/2),!i.isLineHolder&&pn(l)&&gn(t,e,l,"right"===s?o-i.width:"center"===s?o-i.width/2:o,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(o=bn(o,s,c),u-=i.height/2-c[2]-i.textHeight/2),yn(e,"shadowBlur",k(l.textShadowBlur,n.textShadowBlur,0)),yn(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),yn(e,"shadowOffsetX",k(l.textShadowOffsetX,n.textShadowOffsetX,0)),yn(e,"shadowOffsetY",k(l.textShadowOffsetY,n.textShadowOffsetY,0)),yn(e,"textAlign",s),yn(e,"textBaseline","middle"),yn(e,"font",i.font||Ag);
var d=xn(l.textStroke||n.textStroke,p),f=_n(l.textFill||n.textFill),p=A(l.textStrokeWidth,n.textStrokeWidth);d&&(yn(e,"lineWidth",p),yn(e,"strokeStyle",d),e.strokeText(i.text,o,u)),f&&(yn(e,"fillStyle",f),e.fillText(i.text,o,u))}function pn(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function gn(t,e,i,n,r,a,o){var s=i.textBackgroundColor,l=i.textBorderWidth,h=i.textBorderColor,u=b(s);if(yn(e,"shadowBlur",i.textBoxShadowBlur||0),yn(e,"shadowColor",i.textBoxShadowColor||"transparent"),yn(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),yn(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=i.textBorderRadius;c?an(e,{x:n,y:r,width:a,height:o,r:c}):e.rect(n,r,a,o),e.closePath()}if(u)if(yn(e,"fillStyle",s),null!=i.fillOpacity){var d=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(S(s)){var f=s.image;f=Ri(f,null,t,vn,s),f&&Fi(f)&&e.drawImage(f,n,r,a,o)}if(l&&h)if(yn(e,"lineWidth",l),yn(e,"strokeStyle",h),null!=i.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function vn(t,e){e.image=t}function mn(t,e,i,n){var r=i.x||0,a=i.y||0,o=i.textAlign,s=i.textVerticalAlign;if(n){var l=i.textPosition;if(l instanceof Array)r=n.x+wn(l[0],n.width),a=n.y+wn(l[1],n.height);else{var h=e&&e.calculateTextPosition?e.calculateTextPosition(Og,i,n):ji(Og,i,n);r=h.x,a=h.y,o=o||h.textAlign,s=s||h.textVerticalAlign}var u=i.textOffset;u&&(r+=u[0],a+=u[1])}return t=t||{},t.baseX=r,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function yn(t,e,i){return t[e]=cg(t,e,i),t[e]}function xn(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function _n(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function wn(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function bn(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function Sn(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Mn(t){t=t||{},ig.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new gg(t.style,this),this._rect=null,this.__clipPaths=null}function In(t){Mn.call(this,t)}function Tn(t){return parseInt(t,10)}function Cn(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Dn(t,e,i){return Hg.copy(t.getBoundingRect()),t.transform&&Hg.applyTransform(t.transform),Wg.width=e,Wg.height=i,!Hg.intersect(Wg)}function An(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0;return!1}function kn(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}function Pn(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}function Ln(t){return"mousewheel"===t&&jf.browser.firefox?"DOMMouseScroll":t}function On(t){var e=t.pointerType;return"pen"===e||"touch"===e}function En(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}function zn(t){t&&(t.zrByTouch=!0)}function Bn(t,e){return xe(t.dom,new Nn(t,e),!0)}function Rn(t,e){var i=!1;do e=e&&e.parentNode;while(e&&9!==e.nodeType&&!(i=e===t.painterRoot));return i}function Nn(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}function Fn(t,e){var i=e.domHandlers;jf.pointerEventsSupported?f(jg.pointer,function(n){Hn(e,n,function(e){i[n].call(t,e)})}):(jf.touchEventsSupported&&f(jg.touch,function(n){Hn(e,n,function(r){i[n].call(t,r),En(e)})}),f(jg.mouse,function(n){Hn(e,n,function(r){r=ye(r),e.touching||i[n].call(t,r)})}))}function Vn(t,e){function i(i){function n(n){n=ye(n),Rn(t,n.target)||(n=Bn(t,n),e.domHandlers[i].call(t,n))}Hn(e,i,n,{capture:!0})}jf.pointerEventsSupported?f(qg.pointer,i):jf.touchEventsSupported||f(qg.mouse,i)}function Hn(t,e,i,n){t.mounted[e]=i,t.listenerOpts[e]=n,_e(t.domTarget,Ln(e),i,n)}function Wn(t){var e=t.mounted;for(var i in e)e.hasOwnProperty(i)&&we(t.domTarget,Ln(i),e[i],t.listenerOpts[i]);t.mounted={}}function Gn(t,e){if(t._mayPointerCapture=null,Ug&&t._pointerCapturing^e){t._pointerCapturing=e;var i=t._globalHandlerScope;e?Vn(t,i):Wn(i)}}function Xn(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function Yn(t,e){gp.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new Xn(t,Kg),Ug&&(this._globalHandlerScope=new Xn(document,$g)),this._pointerCapturing=!1,this._mayPointerCapture=null,Fn(this,this._localHandlerScope)}function Un(t,e){var i=new nv(Yf(),t,e);return ev[i.id]=i,i}function jn(t){if(t)t.dispose();else{for(var e in ev)ev.hasOwnProperty(e)&&ev[e].dispose();ev={}}return this}function qn(t){return ev[t]}function Zn(t,e){tv[t]=e}function Kn(t){delete ev[t]}function $n(t){return t instanceof Array?t:null==t?[]:[t]}function Qn(t,e,i){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var n=0,r=i.length;r>n;n++){var a=i[n];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}function Jn(t){return!ov(t)||sv(t)||t instanceof Date?t:t.value}function tr(t){return ov(t)&&!(t instanceof Array)}function er(t,e){e=(e||[]).slice();var i=p(t||[],function(t){return{exist:t}});return av(e,function(t,n){if(ov(t)){for(var r=0;r<i.length;r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i.length;r++){var a=i[r].exist;if(!(i[r].option||null!=a.id&&null!=t.id||null==t.name||rr(t)||rr(a)||a.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),av(e,function(t){if(ov(t)){for(var e=0;e<i.length;e++){var n=i[e].exist;if(!i[e].option&&!rr(n)&&null==t.id){i[e].option=t;break}}e>=i.length&&i.push({option:t})}}),i}function ir(t){var e=N();av(t,function(t){var i=t.exist;i&&e.set(i.id,t)}),av(t,function(t){var i=t.option;O(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),av(t,function(t,i){var n=t.exist,r=t.option,a=t.keyInfo;if(ov(r)){if(a.name=null!=r.name?r.name+"":n?n.name:lv+i,n)a.id=n.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\x00"+a.name+"\x00"+o++;while(e.get(a.id))}e.set(a.id,t)}})}function nr(t){var e=t.name;return!(!e||!e.indexOf(lv))}function rr(t){return ov(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function ar(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?_(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?_(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function or(){var t="__\x00ec_inner_"+uv++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function sr(t,e,i){if(b(e)){var n={};n[e+"Index"]=0,e=n}var r=i&&i.defaultMainType;!r||lr(e,r+"Index")||lr(e,r+"Id")||lr(e,r+"Name")||(e[r+"Index"]=0);var a={};return av(e,function(n,r){var n=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=n);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=o[1],l=(o[2]||"").toLowerCase();if(!(!s||!l||null==n||"index"===l&&"none"===n||i&&i.includeMainTypes&&h(i.includeMainTypes,s)<0)){var u={mainType:s};("index"!==l||"all"!==n)&&(u[l]=n);var c=t.queryComponents(u);a[s+"Models"]=c,a[s+"Model"]=c[0]}}),a}function lr(t,e){return t&&t.hasOwnProperty(e)}function hr(t,e,i){t.setAttribute?t.setAttribute(e,i):t[e]=i}function ur(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function cr(t){return"auto"===t?jf.domSupported?"html":"richText":t||"html"}function dr(t){var e={main:"",sub:""};return t&&(t=t.split(cv),e.main=t[0]||"",e.sub=t[1]||""),e}function fr(t){O(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function pr(t){t.$constructor=t,t.extend=function(t){var e=this,i=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o(i.prototype,t),i.extend=this.extend,i.superCall=vr,i.superApply=mr,u(i,this),i.superClass=e,i}}function gr(t){var e=["__\x00is_clz",fv++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function vr(t,e){var i=P(arguments,2);return this.superClass.prototype[e].apply(t,i)}function mr(t,e,i){return this.superClass.prototype[e].apply(t,i)}function yr(t,e){function i(t){var e=n[t.main];return e&&e[dv]||(e=n[t.main]={},e[dv]=!0),e}e=e||{};var n={};if(t.registerClass=function(t,e){if(e)if(fr(e),e=dr(e),e.sub){if(e.sub!==dv){var r=i(e);r[e.sub]=t}}else n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[dv]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=dr(t);var e=[],i=n[t.main];return i&&i[dv]?f(i,function(t,i){i!==dv&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=dr(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(n,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=dr(t);var e=n[t.main];return e&&e[dv]},t.parseClassType=dr,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var i=r.call(this,e);return t.registerClass(i,e.type)})}return t}function xr(t){return t>-wv&&wv>t}function _r(t){return t>wv||-wv>t}function wr(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function br(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function Sr(t,e,i,n,r,a){var o=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*o*l,c=s*l-9*o*h,d=l*l-3*s*h,f=0;if(xr(u)&&xr(c))if(xr(s))a[0]=0;else{var p=-l/s;p>=0&&1>=p&&(a[f++]=p)}else{var g=c*c-4*u*d;if(xr(g)){var v=c/u,p=-s/o+v,m=-v/2;p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m)}else if(g>0){var y=_v(g),x=u*s+1.5*o*(-c+y),_=u*s+1.5*o*(-c-y);x=0>x?-xv(-x,Mv):xv(x,Mv),_=0>_?-xv(-_,Mv):xv(_,Mv);var p=(-s-(x+_))/(3*o);p>=0&&1>=p&&(a[f++]=p)}else{var w=(2*u*s-3*o*c)/(2*_v(u*u*u)),b=Math.acos(w)/3,S=_v(u),M=Math.cos(b),p=(-s-2*S*M)/(3*o),m=(-s+S*(M+Sv*Math.sin(b)))/(3*o),I=(-s+S*(M-Sv*Math.sin(b)))/(3*o);p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m),I>=0&&1>=I&&(a[f++]=I)}}return f}function Mr(t,e,i,n,r){var a=6*i-12*e+6*t,o=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(xr(o)){if(_r(a)){var h=-s/a;h>=0&&1>=h&&(r[l++]=h)}}else{var u=a*a-4*o*s;if(xr(u))r[0]=-a/(2*o);else if(u>0){var c=_v(u),h=(-a+c)/(2*o),d=(-a-c)/(2*o);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Ir(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,h=(s-o)*r+o,u=(l-s)*r+s,c=(u-h)*r+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=n}function Tr(t,e,i,n,r,a,o,s,l,h,u){var c,d,f,p,g,v=.005,m=1/0;Iv[0]=l,Iv[1]=h;for(var y=0;1>y;y+=.05)Tv[0]=wr(t,i,r,o,y),Tv[1]=wr(e,n,a,s,y),p=dp(Iv,Tv),m>p&&(c=y,m=p);m=1/0;for(var x=0;32>x&&!(bv>v);x++)d=c-v,f=c+v,Tv[0]=wr(t,i,r,o,d),Tv[1]=wr(e,n,a,s,d),p=dp(Tv,Iv),d>=0&&m>p?(c=d,m=p):(Cv[0]=wr(t,i,r,o,f),Cv[1]=wr(e,n,a,s,f),g=dp(Cv,Iv),1>=f&&m>g?(c=f,m=g):v*=.5);return u&&(u[0]=wr(t,i,r,o,c),u[1]=wr(e,n,a,s,c)),_v(m)}function Cr(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function Dr(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function Ar(t,e,i,n,r){var a=t-2*e+i,o=2*(e-t),s=t-n,l=0;if(xr(a)){if(_r(o)){var h=-s/o;h>=0&&1>=h&&(r[l++]=h)}}else{var u=o*o-4*a*s;if(xr(u)){var h=-o/(2*a);h>=0&&1>=h&&(r[l++]=h)}else if(u>0){var c=_v(u),h=(-o+c)/(2*a),d=(-o-c)/(2*a);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function kr(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function Pr(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function Lr(t,e,i,n,r,a,o,s,l){var h,u=.005,c=1/0;Iv[0]=o,Iv[1]=s;for(var d=0;1>d;d+=.05){Tv[0]=Cr(t,i,r,d),Tv[1]=Cr(e,n,a,d);var f=dp(Iv,Tv);c>f&&(h=d,c=f)}c=1/0;for(var p=0;32>p&&!(bv>u);p++){var g=h-u,v=h+u;Tv[0]=Cr(t,i,r,g),Tv[1]=Cr(e,n,a,g);var f=dp(Tv,Iv);if(g>=0&&c>f)h=g,c=f;else{Cv[0]=Cr(t,i,r,v),Cv[1]=Cr(e,n,a,v);var m=dp(Cv,Iv);1>=v&&c>m?(h=v,c=m):u*=.5}}return l&&(l[0]=Cr(t,i,r,h),l[1]=Cr(e,n,a,h)),_v(c)}function Or(t,e,i){if(0!==t.length){var n,r=t[0],a=r[0],o=r[0],s=r[1],l=r[1];for(n=1;n<t.length;n++)r=t[n],a=Dv(a,r[0]),o=Av(o,r[0]),s=Dv(s,r[1]),l=Av(l,r[1]);e[0]=a,e[1]=s,i[0]=o,i[1]=l}}function Er(t,e,i,n,r,a){r[0]=Dv(t,i),r[1]=Dv(e,n),a[0]=Av(t,i),a[1]=Av(e,n)}function zr(t,e,i,n,r,a,o,s,l,h){var u,c=Mr,d=wr,f=c(t,i,r,o,Bv);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;f>u;u++){var p=d(t,i,r,o,Bv[u]);l[0]=Dv(p,l[0]),h[0]=Av(p,h[0])}for(f=c(e,n,a,s,Rv),u=0;f>u;u++){var g=d(e,n,a,s,Rv[u]);l[1]=Dv(g,l[1]),h[1]=Av(g,h[1])}l[0]=Dv(t,l[0]),h[0]=Av(t,h[0]),l[0]=Dv(o,l[0]),h[0]=Av(o,h[0]),l[1]=Dv(e,l[1]),h[1]=Av(e,h[1]),l[1]=Dv(s,l[1]),h[1]=Av(s,h[1])}function Br(t,e,i,n,r,a,o,s){var l=kr,h=Cr,u=Av(Dv(l(t,i,r),1),0),c=Av(Dv(l(e,n,a),1),0),d=h(t,i,r,u),f=h(e,n,a,c);o[0]=Dv(t,r,d),o[1]=Dv(e,a,f),s[0]=Av(t,r,d),s[1]=Av(e,a,f)}function Rr(t,e,i,n,r,a,o,s,l){var h=oe,u=se,c=Math.abs(r-a);if(1e-4>c%Lv&&c>1e-4)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(Ov[0]=Pv(r)*i+t,Ov[1]=kv(r)*n+e,Ev[0]=Pv(a)*i+t,Ev[1]=kv(a)*n+e,h(s,Ov,Ev),u(l,Ov,Ev),r%=Lv,0>r&&(r+=Lv),a%=Lv,0>a&&(a+=Lv),r>a&&!o?a+=Lv:a>r&&o&&(r+=Lv),o){var d=a;a=r,r=d}for(var f=0;a>f;f+=Math.PI/2)f>r&&(zv[0]=Pv(f)*i+t,zv[1]=kv(f)*n+e,h(s,zv,s),u(l,zv,l))}function Nr(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,h=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),h=(t*n-i*e)/(t-i);var u=l*a-o+h,c=u*u/(l*l+1);return s/2*s/2>=c}function Fr(t,e,i,n,r,a,o,s,l,h,u){if(0===l)return!1;var c=l;if(u>e+c&&u>n+c&&u>a+c&&u>s+c||e-c>u&&n-c>u&&a-c>u&&s-c>u||h>t+c&&h>i+c&&h>r+c&&h>o+c||t-c>h&&i-c>h&&r-c>h&&o-c>h)return!1;var d=Tr(t,e,i,n,r,a,o,s,h,u,null);return c/2>=d}function Vr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;if(l>e+h&&l>n+h&&l>a+h||e-h>l&&n-h>l&&a-h>l||s>t+h&&s>i+h&&s>r+h||t-h>s&&i-h>s&&r-h>s)return!1;var u=Lr(t,e,i,n,r,a,s,l,null);return h/2>=u}function Hr(t){return t%=$v,0>t&&(t+=$v),t}function Wr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>i||i>u+h)return!1;if(Math.abs(n-r)%Qv<1e-4)return!0;if(a){var c=n;n=Hr(r),r=Hr(c)}else n=Hr(n),r=Hr(r);n>r&&(r+=Qv);var d=Math.atan2(l,s);return 0>d&&(d+=Qv),d>=n&&r>=d||d+Qv>=n&&r>=d+Qv}function Gr(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l===r?1/0:l>r?o:0}function Xr(t,e){return Math.abs(t-e)<em}function Yr(){var t=nm[0];nm[0]=nm[1],nm[1]=t}function Ur(t,e,i,n,r,a,o,s,l,h){if(h>e&&h>n&&h>a&&h>s||e>h&&n>h&&a>h&&s>h)return 0;var u=Sr(e,n,a,s,h,im);if(0===u)return 0;for(var c,d,f=0,p=-1,g=0;u>g;g++){var v=im[g],m=0===v||1===v?.5:1,y=wr(t,i,r,o,v);l>y||(0>p&&(p=Mr(e,n,a,s,nm),nm[1]<nm[0]&&p>1&&Yr(),c=wr(e,n,a,s,nm[0]),p>1&&(d=wr(e,n,a,s,nm[1]))),f+=2===p?v<nm[0]?e>c?m:-m:v<nm[1]?c>d?m:-m:d>s?m:-m:v<nm[0]?e>c?m:-m:c>s?m:-m)}return f}function jr(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=Ar(e,n,a,s,im);if(0===l)return 0;var h=kr(e,n,a);if(h>=0&&1>=h){for(var u=0,c=Cr(e,n,a,h),d=0;l>d;d++){var f=0===im[d]||1===im[d]?.5:1,p=Cr(t,i,r,im[d]);o>p||(u+=im[d]<h?e>c?f:-f:c>a?f:-f)}return u}var f=0===im[0]||1===im[0]?.5:1,p=Cr(t,i,r,im[0]);return o>p?0:e>a?f:-f}function qr(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);im[0]=-l,im[1]=l;var h=Math.abs(n-r);if(1e-4>h)return 0;if(1e-4>h%tm){n=0,r=tm;var u=a?1:-1;return o>=im[0]+t&&o<=im[1]+t?u:0}if(a){var l=n;n=Hr(r),r=Hr(l)}else n=Hr(n),r=Hr(r);n>r&&(r+=tm);for(var c=0,d=0;2>d;d++){var f=im[d];if(f+t>o){var p=Math.atan2(s,f),u=a?1:-1;0>p&&(p=tm+p),(p>=n&&r>=p||p+tm>=n&&r>=p+tm)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function Zr(t,e,i,n,r){for(var a=0,o=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===Jv.M&&u>1&&(i||(a+=Gr(o,s,l,h,n,r))),1===u&&(o=t[u],s=t[u+1],l=o,h=s),c){case Jv.M:l=t[u++],h=t[u++],o=l,s=h;break;case Jv.L:if(i){if(Nr(o,s,t[u],t[u+1],e,n,r))return!0}else a+=Gr(o,s,t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Jv.C:if(i){if(Fr(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=Ur(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Jv.Q:if(i){if(Vr(o,s,t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=jr(o,s,t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Jv.A:var d=t[u++],f=t[u++],p=t[u++],g=t[u++],v=t[u++],m=t[u++];u+=1;var y=1-t[u++],x=Math.cos(v)*p+d,_=Math.sin(v)*g+f;u>1?a+=Gr(o,s,x,_,n,r):(l=x,h=_);var w=(n-d)*g/p+d;if(i){if(Wr(d,f,g,v,v+m,y,e,w,r))return!0}else a+=qr(d,f,g,v,v+m,y,w,r);o=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case Jv.R:l=o=t[u++],h=s=t[u++];var b=t[u++],S=t[u++],x=l+b,_=h+S;if(i){if(Nr(l,h,x,h,e,n,r)||Nr(x,h,x,_,e,n,r)||Nr(x,_,l,_,e,n,r)||Nr(l,_,l,h,e,n,r))return!0}else a+=Gr(x,h,x,_,n,r),a+=Gr(l,_,l,h,n,r);break;case Jv.Z:if(i){if(Nr(o,s,l,h,e,n,r))return!0}else a+=Gr(o,s,l,h,n,r);o=l,s=h}}return i||Xr(s,h)||(a+=Gr(o,s,l,h,n,r)||0),0!==a}function Kr(t,e,i){return Zr(t,0,!1,e,i)}function $r(t,e,i,n){return Zr(t,e,!0,i,n)}function Qr(t){Mn.call(this,t),this.path=null}function Jr(t,e,i,n,r,a,o,s,l,h,u){var c=l*(gm/180),d=pm(c)*(t-i)/2+fm(c)*(e-n)/2,f=-1*fm(c)*(t-i)/2+pm(c)*(e-n)/2,p=d*d/(o*o)+f*f/(s*s);p>1&&(o*=dm(p),s*=dm(p));var g=(r===a?-1:1)*dm((o*o*s*s-o*o*f*f-s*s*d*d)/(o*o*f*f+s*s*d*d))||0,v=g*o*f/s,m=g*-s*d/o,y=(t+i)/2+pm(c)*v-fm(c)*m,x=(e+n)/2+fm(c)*v+pm(c)*m,_=ym([1,0],[(d-v)/o,(f-m)/s]),w=[(d-v)/o,(f-m)/s],b=[(-1*d-v)/o,(-1*f-m)/s],S=ym(w,b);mm(w,b)<=-1&&(S=gm),mm(w,b)>=1&&(S=0),0===a&&S>0&&(S-=2*gm),1===a&&0>S&&(S+=2*gm),u.addData(h,y,x,o,s,_,S,c,a)}function ta(t){if(!t)return new Kv;for(var e,i=0,n=0,r=i,a=n,o=new Kv,s=Kv.CMD,l=t.match(xm),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(_m)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,x,_,w,b,S,M=i,I=n;switch(d){case"l":i+=f[v++],n+=f[v++],u=s.L,o.addData(u,i,n);break;case"L":i=f[v++],n=f[v++],u=s.L,o.addData(u,i,n);break;case"m":i+=f[v++],n+=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="l";break;case"M":i=f[v++],n=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="L";break;case"h":i+=f[v++],u=s.L,o.addData(u,i,n);break;case"H":i=f[v++],u=s.L,o.addData(u,i,n);break;case"v":n+=f[v++],u=s.L,o.addData(u,i,n);break;case"V":n=f[v++],u=s.L,o.addData(u,i,n);break;case"C":u=s.C,o.addData(u,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":u=s.C,o.addData(u,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":m=i,y=n;var T=o.len(),C=o.data;e===s.C&&(m+=i-C[T-4],y+=n-C[T-3]),u=s.C,M=f[v++],I=f[v++],i=f[v++],n=f[v++],o.addData(u,m,y,M,I,i,n);break;case"s":m=i,y=n;var T=o.len(),C=o.data;e===s.C&&(m+=i-C[T-4],y+=n-C[T-3]),u=s.C,M=i+f[v++],I=n+f[v++],i+=f[v++],n+=f[v++],o.addData(u,m,y,M,I,i,n);break;case"Q":M=f[v++],I=f[v++],i=f[v++],n=f[v++],u=s.Q,o.addData(u,M,I,i,n);break;case"q":M=f[v++]+i,I=f[v++]+n,i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,M,I,i,n);break;case"T":m=i,y=n;var T=o.len(),C=o.data;e===s.Q&&(m+=i-C[T-4],y+=n-C[T-3]),i=f[v++],n=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"t":m=i,y=n;var T=o.len(),C=o.data;e===s.Q&&(m+=i-C[T-4],y+=n-C[T-3]),i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"A":x=f[v++],_=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,I=n,i=f[v++],n=f[v++],u=s.A,Jr(M,I,i,n,b,S,x,_,w,u,o);break;case"a":x=f[v++],_=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,I=n,i+=f[v++],n+=f[v++],u=s.A,Jr(M,I,i,n,b,S,x,_,w,u,o)}}("z"===d||"Z"===d)&&(u=s.Z,o.addData(u),i=r,n=a),e=u}return o.toStatic(),o}function ea(t,e){var i=ta(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(i.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){cm(i,t),this.dirty(!0)},e}function ia(t,e){return new Qr(ea(t,e))}function na(t,e){return Qr.extend(ea(t,e))}function ra(t,e){for(var i=[],n=t.length,r=0;n>r;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),i.push(a.path)}var o=new Qr(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},o}function aa(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function oa(t,e,i){var n=e.points,r=e.smooth;if(n&&n.length>=2){if(r&&"spline"!==r){var a=Dm(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;(i?o:o-1)>s;s++){var l=a[2*s],h=a[2*s+1],u=n[(s+1)%o];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(n=Cm(n,i)),t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;c>s;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}function sa(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x1,a=e.x2,o=e.y1,s=e.y2;Pm(2*r)===Pm(2*a)?t.x1=t.x2=ha(r,n,!0):(t.x1=r,t.x2=a),Pm(2*o)===Pm(2*s)?t.y1=t.y2=ha(o,n,!0):(t.y1=o,t.y2=s)}}function la(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x,a=e.y,o=e.width,s=e.height;t.x=ha(r,n,!0),t.y=ha(a,n,!0),t.width=Math.max(ha(r+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(ha(a+s,n,!1)-t.y,0===s?0:1)}}function ha(t,e,i){var n=Pm(2*t);return(n+Pm(e))%2===0?n/2:(n+(i?1:-1))/2}function ua(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?br:wr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?br:wr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?Dr:Cr)(t.x1,t.cpx1,t.x2,e),(i?Dr:Cr)(t.y1,t.cpy1,t.y2,e)]}function ca(t){Mn.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function da(t){return Qr.extend(t)}function fa(t,e){return na(t,e)}function pa(t,e){Jm[t]=e}function ga(t){return Jm.hasOwnProperty(t)?Jm[t]:void 0}function va(t,e,i,n){var r=ia(t,e);return i&&("center"===n&&(i=ya(i,r.getBoundingRect())),xa(r,i)),r}function ma(t,e,i){var n=new In({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===i){var r={width:t.width,height:t.height};n.setStyle(ya(e,r))}}});return n}function ya(t,e){var i,n=e.width/e.height,r=t.height*n;r<=t.width?i=t.height:(r=t.width,i=r/n);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-i/2,width:r,height:i}}function xa(t,e){if(t.applyTransform){var i=t.getBoundingRect(),n=i.calculateTransform(e);t.applyTransform(n)}}function _a(t){return sa(t.shape,t.shape,t.style),t}function wa(t){return la(t.shape,t.shape,t.style),t}function ba(t){return null!=t&&"none"!==t}function Sa(t){if("string"!=typeof t)return t;var e=iy.get(t);return e||(e=Je(t,-.1),1e4>ny&&(iy.set(t,e),ny++)),e}function Ma(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var i=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var n=t.style;for(var r in e)null!=e[r]&&(i[r]=n[r]);i.fill=n.fill,i.stroke=n.stroke}}function Ia(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var i=t.__zr,n=t.useHoverLayer&&i&&"canvas"===i.painter.type;if(t.__highlighted=n?"layer":"plain",!(t.isGroup||!i&&t.useHoverLayer)){var r=t,a=t.style;n&&(r=i.addHover(t),a=r.style),qa(a),n||Ma(r),a.extendFrom(e),Ta(a,e,"fill"),Ta(a,e,"stroke"),ja(a),n||(t.dirty(!1),t.z2+=jm)}}}function Ta(t,e,i){!ba(e[i])&&ba(t[i])&&(t[i]=Sa(t[i]))}function Ca(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var i=t.style,n=t.__cachedNormalStl;n&&(qa(i),t.setStyle(n),ja(i));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===jm&&(t.z2=r)}}function Da(t,e,i){var n,r=Km,a=Km;t.__highlighted&&(r=Zm,n=!0),e(t,i),t.__highlighted&&(a=Zm,n=!0),t.isGroup&&t.traverse(function(t){!t.isGroup&&e(t,i)}),n&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,a)}function Aa(t,e){e=t.__hoverStl=e!==!1&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Ca(t),Ia(t))}function ka(t){!Ea(this,t)&&!this.__highByOuter&&Da(this,Ia)}function Pa(t){!Ea(this,t)&&!this.__highByOuter&&Da(this,Ca)}function La(t){this.__highByOuter|=1<<(t||0),Da(this,Ia)}function Oa(t){!(this.__highByOuter&=~(1<<(t||0)))&&Da(this,Ca)}function Ea(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function za(t,e){Ba(t,!0),Da(t,Aa,e)}function Ba(t,e){var i=e===!1;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!i||t.__highDownDispatcher){var n=i?"off":"on";t[n]("mouseover",ka)[n]("mouseout",Pa),t[n]("emphasis",La)[n]("normal",Oa),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!i}}function Ra(t){return!(!t||!t.__highDownDispatcher)}function Na(t){var e=Qm[t];return null==e&&32>=$m&&(e=Qm[t]=$m++),e}function Fa(t,e,i,n,r,a,o){r=r||Um;var s,l=r.labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=i.getShallow("show"),d=n.getShallow("show");(c||d)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u)),null==s&&(s=w(r.defaultText)?r.defaultText(h,r):r.defaultText));var f=c?s:null,p=d?A(l?l.getFormattedLabel(h,"emphasis",null,u):null,s):null;(null!=f||null!=p)&&(Ha(t,i,a,r),Ha(e,n,o,r,!0)),t.text=f,e.text=p}function Va(t,e,i){var n=t.style;e&&(qa(n),t.setStyle(e),ja(n)),n=t.__hoverStl,i&&n&&(qa(n),o(n,i),ja(n))}function Ha(t,e,i,n,r){return Ga(t,e,n,r),i&&o(t,i),t}function Wa(t,e,i){var n,r={isRectText:!0};i===!1?n=!0:r.autoColor=i,Ga(t,e,r,n)}function Ga(t,e,i,n){if(i=i||Um,i.isRectText){var r;i.getTextPosition?r=i.getTextPosition(e,n):(r=e.getShallow("position")||(n?null:"inside"),"outside"===r&&(r="top")),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=A(e.getShallow("distance"),n?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=Xa(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);Ya(o[u]={},c,l,i,n)}}return t.rich=o,Ya(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function Xa(t){for(var e;t&&t!==t.ecModel;){var i=(t.option||Um).rich;if(i){e=e||{};for(var n in i)i.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function Ya(t,e,i,n,r,a){i=!r&&i||Um,t.textFill=Ua(e.getShallow("color"),n)||i.color,t.textStroke=Ua(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=A(e.getShallow("textBorderWidth"),i.textBorderWidth),r||(a&&(t.insideRollbackOpt=n,ja(t)),null==t.textFill&&(t.textFill=n.autoColor)),t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&n.disableBox||(t.textBackgroundColor=Ua(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=Ua(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function Ua(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function ja(t){var e,i=t.textPosition,n=t.insideRollbackOpt;if(n&&null==t.textFill){var r=n.autoColor,a=n.isRectText,o=n.useInsideStyle,s=o!==!1&&(o===!0||a&&i&&"string"==typeof i&&i.indexOf("inside")>=0),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function qa(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Za(t,e){var i=e&&e.getModel("textStyle");return E([t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" "))}function Ka(t,e,i,n,r,a){"function"==typeof r&&(a=r,r=null);var o=n&&n.isAnimationEnabled();if(o){var s=t?"Update":"",l=n.getShallow("animationDuration"+s),h=n.getShallow("animationEasing"+s),u=n.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(i,l,u||0,h,a,!!a):(e.stopAnimation(),e.attr(i),a&&a())}else e.stopAnimation(),e.attr(i),a&&a()}function $a(t,e,i,n,r){Ka(!0,t,e,i,n,r)}function Qa(t,e,i,n,r){Ka(!1,t,e,i,n,r)}function Ja(t,e){for(var i=ke([]);t&&t!==e;)Le(i,t.getLocalTransform(),i),t=t.parent;return i}function to(t,e,i){return e&&!d(e)&&(e=Pp.getLocalTransform(e)),i&&(e=Be([],e)),ae([],t,e)}function eo(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return a=to(a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function io(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:G(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),$a(t,n,i,t.dataIndex)}}})}}function no(t,e){return p(t,function(t){var i=t[0];i=Xm(i,e.x),i=Ym(i,e.x+e.width);var n=t[1];return n=Xm(n,e.y),n=Ym(n,e.y+e.height),[i,n]})}function ro(t,e){var i=Xm(t.x,e.x),n=Ym(t.x+t.width,e.x+e.width),r=Xm(t.y,e.y),a=Ym(t.y+t.height,e.y+e.height);return n>=i&&a>=r?{x:i,y:r,width:n-i,height:a-r}:void 0}function ao(t,e,i){e=o({rectHover:!0},e);var n=e.style={strokeNoScale:!0};return i=i||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(n.image=t.slice(8),s(n,i),new In(e)):va(t.replace("path://",""),e,i,"center"):void 0}function oo(t,e,i,n,r){for(var a=0,o=r[r.length-1];a<r.length;a++){var s=r[a];if(so(t,e,i,n,s[0],s[1],o[0],o[1]))return!0;o=s}}function so(t,e,i,n,r,a,o,s){var l=i-t,h=n-e,u=o-r,c=s-a,d=lo(u,c,l,h);if(ho(d))return!1;var f=t-r,p=e-a,g=lo(f,p,l,h)/d;if(0>g||g>1)return!1;var v=lo(f,p,u,c)/d;return 0>v||v>1?!1:!0}function lo(t,e,i,n){return t*n-i*e}function ho(t){return 1e-6>=t&&t>=-1e-6}function uo(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function co(t,e,i){for(var n=0;n<e.length&&(!e[n]||(t=t&&"object"==typeof t?t[e[n]]:null,null!=t));n++);return null==t&&i&&(t=i.get(e)),t}function fo(t,e){var i=uy(t).getParent;return i?i.call(t,e):t.parentModel}function po(t){return[t||"",cy++,Math.random().toFixed(5)].join("_")}function go(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=dr(t),e[t.main]=i},t.determineSubType=function(i,n){var r=n.type;if(!r){var a=dr(i).main;t.hasSubTypes(i)&&e[a]&&(r=e[a](n))}return r},t}function vo(t,e){function i(t){var i={},a=[];return f(t,function(o){var s=n(i,o),l=s.originalDeps=e(o),u=r(l,t);s.entryCount=u.length,0===s.entryCount&&a.push(o),f(u,function(t){h(s.predecessor,t)<0&&s.predecessor.push(t);var e=n(i,t);h(e.successor,t)<0&&e.successor.push(o)})}),{graph:i,noEntryList:a}}function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var i=[];return f(t,function(t){h(e,t)>=0&&i.push(t)}),i}t.topologicalTravel=function(t,e,n,r){function a(t){l[t].entryCount--,0===l[t].entryCount&&h.push(t)
}function o(t){u[t]=!0,a(t)}if(t.length){var s=i(e),l=s.graph,h=s.noEntryList,u={};for(f(t,function(t){u[t]=!0});h.length;){var c=h.pop(),d=l[c],p=!!u[c];p&&(n.call(r,c,d.originalDeps.slice()),delete u[c]),f(d.successor,p?o:a)}f(u,function(){throw new Error("Circle dependency may exists")})}}}function mo(t){return t.replace(/^\s+|\s+$/g,"")}function yo(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]}function xo(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?mo(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function _o(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function wo(t){return t.sort(function(t,e){return t-e}),t}function bo(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i}function So(t){var e=t.toString(),i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function Mo(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function Io(t,e,i){if(!t[e])return 0;var n=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var r=Math.pow(10,i),a=p(t,function(t){return(isNaN(t)?0:t)/n*r*100}),o=100*r,s=p(a,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),h=p(a,function(t,e){return t-s[e]});o>l;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,f=h.length;f>d;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function To(t){var e=2*Math.PI;return(t%e+e)%e}function Co(t){return t>-dy&&dy>t}function Do(t){if(t instanceof Date)return t;if("string"==typeof t){var e=py.exec(t);if(!e)return new Date(0/0);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function Ao(t){return Math.pow(10,ko(t))}function ko(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Po(t,e){var i,n=ko(t),r=Math.pow(10,n),a=t/r;return i=e?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,t=i*r,n>=-20?+t.toFixed(0>n?-n:0):t}function Lo(t,e){var i=(t.length-1)*e+1,n=Math.floor(i),r=+t[n-1],a=i-n;return a?r+a*(t[n]-r):r}function Oo(t){function e(t,i,n){return t.interval[n]<i.interval[n]||t.interval[n]===i.interval[n]&&(t.close[n]-i.close[n]===(n?-1:1)||!n&&e(t,i,1))}t.sort(function(t,i){return e(t,i,0)?-1:1});for(var i=-1/0,n=1,r=0;r<t.length;){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=i&&(a[s]=i,o[s]=s?1:1-n),i=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t.splice(r,1):r++}return t}function Eo(t){return t-parseFloat(t)>=0}function zo(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function Bo(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Ro(t){return null==t?"":(t+"").replace(my,function(t,e){return yy[e]})}function No(t,e,i){_(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],a=0;a<r.length;a++){var o=xy[a];t=t.replace(_y(o),_y(o,0))}for(var s=0;n>s;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(_y(xy[l],s),i?Ro(h):h)}return t}function Fo(t,e,i){return f(e,function(e,n){t=t.replace("{"+n+"}",i?Ro(e):e)}),t}function Vo(t,e){t=b(t)?{color:t,extraCssText:e}:t||{};var i=t.color,n=t.type,e=t.extraCssText,r=t.renderMode||"html",a=t.markerId||"X";return i?"html"===r?"subItem"===n?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Ro(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Ro(i)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+a+"|}  ",style:{color:i}}:""}function Ho(t,e){return t+="","0000".substr(0,e-t.length)+t}function Wo(t,e,i){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=Do(e),r=i?"UTC":"",a=n["get"+r+"FullYear"](),o=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),h=n["get"+r+"Minutes"](),u=n["get"+r+"Seconds"](),c=n["get"+r+"Milliseconds"]();return t=t.replace("MM",Ho(o,2)).replace("M",o).replace("yyyy",a).replace("yy",a%100).replace("dd",Ho(s,2)).replace("d",s).replace("hh",Ho(l,2)).replace("h",l).replace("mm",Ho(h,2)).replace("m",h).replace("ss",Ho(u,2)).replace("s",u).replace("SSS",Ho(c,3))}function Go(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function Xo(t){return Wi(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function Yo(t,e,i,n,r,a,o,s){return Wi(t,e,i,n,r,s,a,o)}function Uo(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+i,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+i,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=u+i:o=c+i)})}function jo(t,e,i){i=vy(i||0);var n=e.width,r=e.height,a=xo(t.left,n),o=xo(t.top,r),s=xo(t.right,n),l=xo(t.bottom,r),h=xo(t.width,n),u=xo(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(h)&&(h=n-s-d-a),isNaN(u)&&(u=r-l-c-o),null!=f&&(isNaN(h)&&isNaN(u)&&(f>n/r?h=.8*n:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(a)&&(a=n-s-h-d),isNaN(o)&&(o=r-l-u-c),t.left||t.right){case"center":a=n/2-h/2-i[3];break;case"right":a=n-h-d}switch(t.top||t.bottom){case"middle":case"center":o=r/2-u/2-i[0];break;case"bottom":o=r-u-c}a=a||0,o=o||0,isNaN(h)&&(h=n-d-a-(s||0)),isNaN(u)&&(u=r-c-o-(l||0));var p=new bi(a+i[3],o+i[0],h,u);return p.margin=i,p}function qo(t,e,i){function n(i,n){var o={},l=0,h={},u=0,c=2;if(Sy(i,function(e){h[e]=t[e]}),Sy(i,function(t){r(e,t)&&(o[t]=h[t]=e[t]),a(o,t)&&l++,a(h,t)&&u++}),s[n])return a(e,i[1])?h[i[2]]=null:a(e,i[2])&&(h[i[1]]=null),h;if(u!==c&&l){if(l>=c)return o;for(var d=0;d<i.length;d++){var f=i[d];if(!r(o,f)&&r(t,f)){o[f]=t[f];break}}return o}return h}function r(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function o(t,e,i){Sy(t,function(t){e[t]=i[t]})}!S(i)&&(i={});var s=i.ignoreSize;!_(s)&&(s=[s,s]);var l=n(Iy[0],0),h=n(Iy[1],1);o(Iy[0],t,l),o(Iy[1],t,h)}function Zo(t){return Ko({},t)}function Ko(t,e){return e&&t&&Sy(My,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t}function $o(t){var e=[];return f(Ay.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return dr(t).main}),"dataset"!==t&&h(e,"dataset")<=0&&e.unshift("dataset"),e}function Qo(t,e){for(var i=t.length,n=0;i>n;n++)if(t[n].length>e)return t[n];return t[i-1]}function Jo(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===Ry?{}:[]),this.sourceFormat=t.sourceFormat||Ny,this.seriesLayoutBy=t.seriesLayoutBy||Vy,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&N(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function ts(t){var e=t.option.source,i=Ny;if(I(e))i=Fy;else if(_(e)){0===e.length&&(i=zy);for(var n=0,r=e.length;r>n;n++){var a=e[n];if(null!=a){if(_(a)){i=zy;break}if(S(a)){i=By;break}}}}else if(S(e)){for(var o in e)if(e.hasOwnProperty(o)&&d(e[o])){i=Ry;break}}else if(null!=e)throw new Error("Invalid data");Gy(t).sourceFormat=i}function es(t){return Gy(t).source}function is(t){Gy(t).datasetMap=N()}function ns(t){var e=t.option,i=e.data,n=I(i)?Fy:Ey,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=us(t);if(l){var h=l.option;i=h.source,n=Gy(l).sourceFormat,r=!0,a=a||h.seriesLayoutBy,null==o&&(o=h.sourceHeader),s=s||h.dimensions}var u=rs(i,n,a,o,s);Gy(t).source=new Jo({data:i,fromDataset:r,seriesLayoutBy:a,sourceFormat:n,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:e.encode})}function rs(t,e,i,n,r){if(!t)return{dimensionsDefine:as(r)};var a,o;if(e===zy)"auto"===n||null==n?os(function(t){null!=t&&"-"!==t&&(b(t)?null==o&&(o=1):o=0)},i,t,10):o=n?1:0,r||1!==o||(r=[],os(function(t,e){r[e]=null!=t?t:""},i,t)),a=r?r.length:i===Hy?t.length:t[0]?t[0].length:null;else if(e===By)r||(r=ss(t));else if(e===Ry)r||(r=[],f(t,function(t,e){r.push(e)}));else if(e===Ey){var s=Jn(t[0]);a=_(s)&&s.length||1}return{startIndex:o,dimensionsDefine:as(r),dimensionsDetectCount:a}}function as(t){if(t){var e=N();return p(t,function(t){if(t=o({},S(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function os(t,e,i,n){if(null==n&&(n=1/0),e===Hy)for(var r=0;r<i.length&&n>r;r++)t(i[r]?i[r][0]:null,r);else for(var a=i[0]||[],r=0;r<a.length&&n>r;r++)t(a[r],r)}function ss(t){for(var e,i=0;i<t.length&&!(e=t[i++]););if(e){var n=[];return f(e,function(t,e){n.push(e)}),n}}function ls(t,e,i){function n(t,e,i){for(var n=0;i>n;n++)t.push(e+n)}function r(t){var e=t.dimsDef;return e?e.length:1}var a={},o=us(e);if(!o||!t)return a;var s,l,h=[],u=[],c=e.ecModel,d=Gy(c).datasetMap,p=o.uid+"_"+i.seriesLayoutBy;t=t.slice(),f(t,function(e,i){!S(e)&&(t[i]={name:e}),"ordinal"===e.type&&null==s&&(s=i,l=r(t[i])),a[e.name]=[]});var g=d.get(p)||d.set(p,{categoryWayDim:l,valueWayDim:0});return f(t,function(t,e){var i=t.name,o=r(t);if(null==s){var l=g.valueWayDim;n(a[i],l,o),n(u,l,o),g.valueWayDim+=o}else if(s===e)n(a[i],0,o),n(h,0,o);else{var l=g.categoryWayDim;n(a[i],l,o),n(u,l,o),g.categoryWayDim+=o}}),h.length&&(a.itemName=h),u.length&&(a.seriesName=u),a}function hs(t,e,i){var n={},r=us(t);if(!r)return n;var a,o=e.sourceFormat,s=e.dimensionsDefine;(o===By||o===Ry)&&f(s,function(t,e){"name"===(S(t)?t.name:t)&&(a=e)});var l=function(){function t(t){return null!=t.v&&null!=t.n}for(var n={},r={},l=[],h=0,u=Math.min(5,i);u>h;h++){var c=ds(e.data,o,e.seriesLayoutBy,s,e.startIndex,h);l.push(c);var d=c===Wy.Not;if(d&&null==n.v&&h!==a&&(n.v=h),(null==n.n||n.n===n.v||!d&&l[n.n]===Wy.Not)&&(n.n=h),t(n)&&l[n.n]!==Wy.Not)return n;d||(c===Wy.Might&&null==r.v&&h!==a&&(r.v=h),(null==r.n||r.n===r.v)&&(r.n=h))}return t(n)?n:t(r)?r:null}();if(l){n.value=l.v;var h=null!=a?a:l.n;n.itemName=[h],n.seriesName=[h]}return n}function us(t){var e=t.option,i=e.data;return i?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function cs(t,e){return ds(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function ds(t,e,i,n,r,a){function o(t){var e=b(t);return null!=t&&isFinite(t)&&""!==t?e?Wy.Might:Wy.Not:e&&"-"!==t?Wy.Must:void 0}var s,l=5;if(I(t))return Wy.Not;var h,u;if(n){var c=n[a];S(c)?(h=c.name,u=c.type):b(c)&&(h=c)}if(null!=u)return"ordinal"===u?Wy.Must:Wy.Not;if(e===zy)if(i===Hy){for(var d=t[a],f=0;f<(d||[]).length&&l>f;f++)if(null!=(s=o(d[r+f])))return s}else for(var f=0;f<t.length&&l>f;f++){var p=t[r+f];if(p&&null!=(s=o(p[a])))return s}else if(e===By){if(!h)return Wy.Not;for(var f=0;f<t.length&&l>f;f++){var g=t[f];if(g&&null!=(s=o(g[h])))return s}}else if(e===Ry){if(!h)return Wy.Not;var d=t[h];if(!d||I(d))return Wy.Not;for(var f=0;f<d.length&&l>f;f++)if(null!=(s=o(d[f])))return s}else if(e===Ey)for(var f=0;f<t.length&&l>f;f++){var g=t[f],v=Jn(g);if(!_(v))return Wy.Not;if(null!=(s=o(v[a])))return s}return Wy.Not}function fs(t,e){if(e){var i=e.seiresIndex,n=e.seriesId,r=e.seriesName;return null!=i&&t.componentIndex!==i||null!=n&&t.id!==n||null!=r&&t.name!==r}}function ps(t,e){var i=t.color&&!t.colorLayer;f(e,function(e,a){"colorLayer"===a&&i||Ay.hasClass(a)||("object"==typeof e?t[a]=t[a]?r(t[a],e,!1):n(e):null==t[a]&&(t[a]=e))})}function gs(t){t=t,this.option={},this.option[Xy]=1,this._componentsMap=N({series:[]}),this._seriesIndices,this._seriesIndicesMap,ps(t,this._theme.option),r(t,Py,!1),this.mergeOption(t)}function vs(t,e){_(e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t.get(e)||[]).slice()}),i}function ms(t,e,i){var n=e.type?e.type:i?i.subType:Ay.determineSubType(t,e);return n}function ys(t,e){t._seriesIndicesMap=N(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function xs(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function _s(t){f(Uy,function(e){this[e]=y(t[e],t)},this)}function ws(){this._coordinateSystems=[]}function bs(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function Ss(t,e,i){var n,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;qy(l,function(t){t&&t.option&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=s),qy([r].concat(a).concat(p(o,function(t){return t.option})),function(t){qy(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function Ms(t,e,i){var n={width:e,height:i,aspectratio:e/i},r=!0;return f(t,function(t,e){var i=e.match(Qy);if(i&&i[1]&&i[2]){var a=i[1],o=i[2].toLowerCase();Is(n[o],t,a)||(r=!1)}}),r}function Is(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function Ts(t,e){return t.join(",")===e.join(",")}function Cs(t,e){e=e||{},qy(e,function(e,i){if(null!=e){var n=t[i];if(Ay.hasClass(i)){e=$n(e),n=$n(n);var r=er(n,e);t[i]=Ky(r,function(t){return t.option&&t.exist?$y(t.exist,t.option,!0):t.exist||t.option})}else t[i]=$y(n,e,!0)}})}function Ds(t){var e=t&&t.itemStyle;if(e)for(var i=0,n=ex.length;n>i;i++){var a=ex[i],o=e.normal,s=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?r(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),s&&s[a]&&(t[a]=t[a]||{},t[a].emphasis?r(t[a].emphasis,s[a]):t[a].emphasis=s[a],s[a]=null)}}function As(t,e,i){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var n=t[e].normal,r=t[e].emphasis;n&&(i?(t[e].normal=t[e].emphasis=null,s(t[e],n)):t[e]=n),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function ks(t){As(t,"itemStyle"),As(t,"lineStyle"),As(t,"areaStyle"),As(t,"label"),As(t,"labelLine"),As(t,"upperLabel"),As(t,"edgeLabel")}function Ps(t,e){var i=tx(t)&&t[e],n=tx(i)&&i.textStyle;if(n)for(var r=0,a=hv.length;a>r;r++){var e=hv[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function Ls(t){t&&(ks(t),Ps(t,"label"),t.emphasis&&Ps(t.emphasis,"label"))}function Os(t){if(tx(t)){Ds(t),ks(t),Ps(t,"label"),Ps(t,"upperLabel"),Ps(t,"edgeLabel"),t.emphasis&&(Ps(t.emphasis,"label"),Ps(t.emphasis,"upperLabel"),Ps(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Ds(e),Ls(e));var i=t.markLine;i&&(Ds(i),Ls(i));var n=t.markArea;n&&Ls(n);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!I(a))for(var o=0;o<a.length;o++)Ls(a[o]);f(t.categories,function(t){ks(t)})}if(r&&!I(r))for(var o=0;o<r.length;o++)Ls(r[o]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,o=0;o<s.length;o++)Ls(s[o]);var i=t.markLine;if(i&&i.data)for(var l=i.data,o=0;o<l.length;o++)_(l[o])?(Ls(l[o][0]),Ls(l[o][1])):Ls(l[o]);"gauge"===t.type?(Ps(t,"axisLabel"),Ps(t,"title"),Ps(t,"detail")):"treemap"===t.type?(As(t.breadcrumb,"itemStyle"),f(t.levels,function(t){ks(t)})):"tree"===t.type&&ks(t.leaves)}}function Es(t){return _(t)?t:t?[t]:[]}function zs(t){return(_(t)?t[0]:t)||{}}function Bs(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&(i=i&&i[e[n]],null!=i);n++);return i}function Rs(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function Ns(t){f(nx,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function Fs(t){f(t,function(e,i){var n=[],r=[0/0,0/0],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,h){var u=o.get(e.stackedDimension,h);if(isNaN(u))return r;var c,d;s?d=o.getRawIndex(h):c=o.get(e.stackedByDimension,h);for(var f=0/0,p=i-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(u>=0&&v>0||0>=u&&0>v){u+=v,f=v;break}}}return n[0]=u,n[1]=f,n});o.hostModel.setData(l),e.data=l})}function Vs(t,e){Jo.isInstance(t)||(t=Jo.seriesDataToSource(t)),this._source=t;var i=this._data=t.data,n=t.sourceFormat;n===Fy&&(this._offset=0,this._dimSize=e,this._data=i);var r=lx[n===zy?n+"_"+t.seriesLayoutBy:n];o(this,r)}function Hs(){return this._data.length}function Ws(t){return this._data[t]}function Gs(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Xs(t,e,i){return null!=i?t[i]:t}function Ys(t,e,i,n){return Us(t[n],this._dimensionInfos[e])}function Us(t,e){var i=e&&e.type;if("ordinal"===i){var n=e&&e.ordinalMeta;return n?n.parseAndCollect(t):t}return"time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Do(t)),null==t||""===t?0/0:+t}function js(t,e,i){if(t){var n=t.getRawDataItem(e);if(null!=n){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(i);return s&&(r=s.name,a=s.index),hx[o](n,e,a,r)}}}function qs(t,e,i){if(t){var n=t.getProvider().getSource().sourceFormat;if(n===Ey||n===By){var r=t.getRawDataItem(e);return n!==Ey||S(r)||(r=null),r?r[i]:void 0}}}function Zs(t){return new Ks(t)}function Ks(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function $s(t,e,i,n,r,a){px.reset(i,n,r,a),t._callingProgress=e,t._callingProgress({start:i,end:n,count:n-i,next:px.next},t.context)}function Qs(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var i,n;!e&&t._reset&&(i=t._reset(t.context),i&&i.progress&&(n=i.forceFirstProgress,i=i.progress),_(i)&&!i.length&&(i=null)),t._progress=i,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),n}function Js(t){var e=t.name;nr(t)||(t.name=tl(t)||e)}function tl(t){var e=t.getRawData(),i=e.mapDimension("seriesName",!0),n=[];return f(i,function(t){var i=e.getDimensionInfo(t);i.displayName&&n.push(i.displayName)}),n.join(" ")}function el(t){return t.model.getRawData().count()}function il(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),nl}function nl(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function rl(t,e){f(t.CHANGABLE_METHODS,function(i){t.wrapMethod(i,x(al,e))})}function al(t){var e=ol(t);e&&e.setOutputEnd(this.count())}function ol(t){var e=(t.ecModel||{}).scheduler,i=e&&e.getPipeline(t.uid);if(i){var n=i.currentTask;if(n){var r=n.agentStubMap;r&&(n=r.get(t.uid))}return n}}function sl(){this.group=new og,this.uid=po("viewChart"),this.renderTask=Zs({plan:ul,reset:cl}),this.renderTask.context={view:this}}function ll(t,e,i){if(t&&(t.trigger(e,i),t.isGroup&&!Ra(t)))for(var n=0,r=t.childCount();r>n;n++)ll(t.childAt(n),e,i)}function hl(t,e,i){var n=ar(t,e),r=e&&null!=e.highlightKey?Na(e.highlightKey):null;null!=n?f($n(n),function(e){ll(t.getItemGraphicEl(e),i,r)}):t.eachItemGraphicEl(function(t){ll(t,i,r)})}function ul(t){return bx(t.model)}function cl(t){var e=t.model,i=t.ecModel,n=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&_x(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,i,n,r),Mx[l]}function dl(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,d=l||i;l=null,a=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(n,t):a>=0?n():c=setTimeout(n,-a),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function fl(t,e,i,n){var r=t[e];if(r){var a=r[Ix]||r,o=r[Cx],s=r[Tx];if(s!==i||o!==n){if(null==i||!n)return t[e]=a;r=t[e]=dl(a,i,"debounce"===n),r[Ix]=a,r[Cx]=n,r[Tx]=i}return r}}function pl(t,e,i,n){this.ecInstance=t,this.api=e,this.unfinished;var i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice();this._allHandlers=i.concat(n),this._stageTaskMap=N()}function gl(t,e,i,n,r){function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var o;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,h=s.overallTask;if(h){var u,c=h.agentStubMap;c.each(function(t){a(r,t)&&(t.dirty(),u=!0)}),u&&h.dirty(),Ex(h,n);var d=t.getPerformArgs(h,r.block);c.each(function(t){t.perform(d)}),o|=h.perform(d)}else l&&l.each(function(s){a(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&i.isSeriesFiltered(s.context.model),Ex(s,n),o|=s.perform(l)})}}),t.unfinished|=o}function vl(t,e,i,n,r){function a(i){var a=i.uid,s=o.get(a)||o.set(a,Zs({plan:bl,reset:Sl,count:Il}));s.context={model:i,ecModel:n,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},Tl(t,i,s)}var o=i.seriesTaskMap||(i.seriesTaskMap=N()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?n.eachRawSeries(a):s?n.eachRawSeriesByType(s,a):l&&l(n,r).each(a);var h=t._pipelineMap;o.each(function(t,e){h.get(e)||(t.dispose(),o.removeKey(e))})}function ml(t,e,i,n,r){function a(e){var i=e.uid,n=s.get(i);n||(n=s.set(i,Zs({reset:xl,onDirty:wl})),o.dirty()),n.context={model:e,overallProgress:u,modifyOutputEnd:c},n.agent=o,n.__block=u,Tl(t,e,n)}var o=i.overallTask=i.overallTask||Zs({reset:yl});o.context={ecModel:n,api:r,overallReset:e.overallReset,scheduler:t};var s=o.agentStubMap=o.agentStubMap||N(),l=e.seriesType,h=e.getTargetSeries,u=!0,c=e.modifyOutputEnd;l?n.eachRawSeriesByType(l,a):h?h(n,r).each(a):(u=!1,f(n.getSeries(),a));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),s.removeKey(e))})}function yl(t){t.overallReset(t.ecModel,t.api,t.payload)}function xl(t){return t.overallProgress&&_l}function _l(){this.agent.dirty(),this.getDownstream().dirty()}function wl(){this.agent&&this.agent.dirty()}function bl(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Sl(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=$n(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return Ml(e)}):zx}function Ml(t){return function(e,i){var n=i.data,r=i.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(n,a);else r&&r.progress&&r.progress(e,n)}}function Il(t){return t.data.count()}function Tl(t,e,i){var n=e.uid,r=t._pipelineMap.get(n);!r.head&&(r.head=i),r.tail&&r.tail.pipe(i),r.tail=i,i.__idxInPipeline=r.count++,i.__pipeline=r}function Cl(t){Bx=null;try{t(Rx,Nx)}catch(e){}return Bx}function Dl(t,e){for(var i in e.prototype)t[i]=V}function Al(t){if(b(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function kl(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function Pl(t,e){for(var i=t.firstChild;i;){if(1===i.nodeType){var n=i.getAttribute("offset");n=n.indexOf("%")>0?parseInt(n,10)/100:n?parseFloat(n):0;var r=i.getAttribute("stop-color")||"#000000";e.addColorStop(n,r)}i=i.nextSibling}}function Ll(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function Ol(t){for(var e=E(t).split(Ux),i=[],n=0;n<e.length;n+=2){var r=parseFloat(e[n]),a=parseFloat(e[n+1]);i.push([r,a])}return i}function El(t,e,i,n){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(Bl(t,e),o(r,Rl(t)),!n))for(var s in Zx)if(Zx.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[Zx[s]]=l)}var h=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new gg;var c=e.style;null!=r.fill&&c.set(h,zl(r.fill,i)),null!=r.stroke&&c.set(u,zl(r.stroke,i)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=E(r.lineDash).split(Ux)),c[u]&&"none"!==c[u]&&(e[u]=!0),e.__inheritedStyle=r}function zl(t,e){var i=e&&t&&t.match(Kx);if(i){var n=E(i[1]),r=e[n];return r}return t}function Bl(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var n=null,r=[];i.replace($x,function(t,e,i){r.push(e,i)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(n=n||Ae(),s){case"translate":o=E(o).split(Ux),Oe(n,n,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=E(o).split(Ux),ze(n,n,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=E(o).split(Ux),Ee(n,n,parseFloat(o[0]));break;case"skew":o=E(o).split(Ux),console.warn("Skew transform is not supported yet");break;case"matrix":var o=E(o).split(Ux);n[0]=parseFloat(o[0]),n[1]=parseFloat(o[1]),n[2]=parseFloat(o[2]),n[3]=parseFloat(o[3]),n[4]=parseFloat(o[4]),n[5]=parseFloat(o[5])}}e.setLocalTransform(n)}}function Rl(t){var e=t.getAttribute("style"),i={};if(!e)return i;var n={};Qx.lastIndex=0;for(var r;null!=(r=Qx.exec(e));)n[r[1]]=r[2];for(var a in Zx)Zx.hasOwnProperty(a)&&null!=n[a]&&(i[Zx[a]]=n[a]);return i}function Nl(t,e,i){var n=e/t.width,r=i/t.height,a=Math.min(n,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+i/2];return{scale:o,position:s}}function Fl(t,e){return function(i,n,r){(e||!this._disposed)&&(i=i&&i.toLowerCase(),gp.prototype[t].call(this,i,n,r))}}function Vl(){gp.call(this)}function Hl(t,e,i){function r(t,e){return t.__prio-e.__prio}i=i||{},"string"==typeof e&&(e=E_[e]),this.id,this.group,this._dom=t;var a="canvas",o=this._zr=Un(t,{renderer:i.renderer||a,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=dl(y(o.flush,o),17);var e=n(e);e&&ax(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new ws;var s=this._api=ah(this);ki(O_,r),ki(k_,r),this._scheduler=new pl(this,s,k_,O_),gp.call(this,this._ecEventProcessor=new oh),this._messageCenter=new Vl,this._initEvents(),this.resize=y(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),Zl(o,this),z(this)}function Wl(t,e,i){if(!this._disposed){var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=sr(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}}function Gl(t){var e=t._model,i=t._scheduler;i.restorePipelines(e),i.prepareStageTasks(),Kl(t,"component",e,i),Kl(t,"chart",e,i),i.plan()}function Xl(t,e,i,n,r){function a(n){n&&n.__alive&&n[e]&&n[e](n.__model,o,t._api,i)}var o=t._model;if(!n)return void n_(t._componentsViews.concat(t._chartsViews),a);var s={};s[n+"Id"]=i[n+"Id"],s[n+"Index"]=i[n+"Index"],s[n+"Name"]=i[n+"Name"];var l={mainType:n,query:s};r&&(l.subType=r);var h=i.excludeSeriesId;null!=h&&(h=N($n(h))),o&&o.eachComponent(l,function(e){h&&null!=h.get(e.id)||a(t["series"===n?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function Yl(t,e){var i=t._chartsMap,n=t._scheduler;e.eachSeries(function(t){n.updateStreamModes(t,i[t.__viewId])})}function Ul(t,e){var i=t.type,n=t.escapeConnect,r=D_[i],a=r.actionInfo,l=(a.update||"update").split(":"),h=l.pop();l=null!=l[0]&&o_(l[0]),this[b_]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=p(t.batch,function(e){return e=s(o({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===i||"downplay"===i;n_(u,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=a.event||d.type,f.push(d),g?Xl(this,h,t,"series"):l&&Xl(this,h,t,l.main,l.sub)},this),"none"===h||g||l||(this[S_]?(Gl(this),T_.update.call(this,t),this[S_]=!1):T_[h].call(this,t)),d=c?{type:a.event||i,escapeConnect:n,batch:f}:f[0],this[b_]=!1,!e&&this._messageCenter.trigger(d.type,d)}function jl(t){for(var e=this._pendingActions;e.length;){var i=e.shift();Ul.call(this,i,t)}}function ql(t){!t&&this.trigger("updated")}function Zl(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[S_]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function Kl(t,e,i,n){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var u=o_(t.type),c=a?mx.getClass(u.main,u.sub):sl.getClass(u.sub);r=new c,r.init(i,h),s[e]=r,o.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!a&&n.prepareView(r,t,i,h)}for(var a="component"===e,o=a?t._componentsViews:t._chartsViews,s=a?t._componentsMap:t._chartsMap,l=t._zr,h=t._api,u=0;u<o.length;u++)o[u].__alive=!1;a?i.eachComponent(function(t,e){"series"!==t&&r(e)}):i.eachSeries(r);for(var u=0;u<o.length;){var c=o[u];c.__alive?u++:(!a&&c.renderTask.dispose(),l.remove(c.group),c.dispose(i,h),o.splice(u,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function $l(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Ql(t,e,i,n){Jl(t,e,i,n),n_(t._chartsViews,function(t){t.__alive=!1}),th(t,e,i,n),n_(t._chartsViews,function(t){t.__alive||t.remove(e,i)})}function Jl(t,e,i,n,r){n_(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,i,n),rh(r,t)})}function th(t,e,i,n,r){var a,o=t._scheduler;e.eachSeries(function(e){var i=t._chartsMap[e.__viewId];i.__alive=!0;var s=i.renderTask;o.updatePayload(s,n),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),i.group.silent=!!e.get("silent"),rh(e,i),nh(e,i)}),o.unfinished|=a,ih(t,e),kx(t._zr.dom,e)}function eh(t,e){n_(L_,function(i){i(t,e)})}function ih(t,e){var i=t._zr,n=i.storage,r=0;n.traverse(function(){r++}),r>e.get("hoverLayerThreshold")&&!jf.node&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var i=t._chartsMap[e.__viewId];i.__alive&&i.group.traverse(function(t){t.useHoverLayer=!0})}})}function nh(t,e){var i=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}function rh(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function ah(t){var e=t._coordSysMgr;return o(new _s(t),{getCoordinateSystems:y(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var i=e.__ecComponentInfo;if(null!=i)return t._model.getComponent(i.mainType,i.index);e=e.parent}}})}function oh(){this.eventInfo}function sh(t){function e(t,e){for(var i=0;i<t.length;i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";n_(A_,function(o,s){t._messageCenter.on(s,function(o){if(R_[t.group]&&t[a]!==i){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];n_(B_,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),n_(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}function lh(t,e,i){var n=dh(t);if(n)return n;var r=new Hl(t,e,i);return r.id="ec_"+N_++,B_[r.id]=r,hr(t,V_,r.id),sh(r),r}function hh(t){if(_(t)){var e=t;t=null,n_(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+F_++,n_(e,function(e){e.group=t})}return R_[t]=!0,t}function uh(t){R_[t]=!1}function ch(t){"string"==typeof t?t=B_[t]:t instanceof Hl||(t=dh(t)),t instanceof Hl&&!t.isDisposed()&&t.dispose()}function dh(t){return B_[ur(t,V_)]}function fh(t){return B_[t]}function ph(t,e){E_[t]=e}function gh(t){P_.push(t)}function vh(t,e){Sh(k_,t,e,u_)
}function mh(t){L_.push(t)}function yh(t,e,i){"function"==typeof e&&(i=e,e="");var n=a_(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,i_(M_.test(n)&&M_.test(e)),D_[n]||(D_[n]={action:i,actionInfo:t}),A_[e]=n}function xh(t,e){ws.register(t,e)}function _h(t){var e=ws.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function wh(t,e){Sh(O_,t,e,p_,"layout")}function bh(t,e){Sh(O_,t,e,m_,"visual")}function Sh(t,e,i,n,r){(r_(e)||a_(e))&&(i=e,e=n);var a=pl.wrapStageHandler(i,r);return a.__prio=e,a.__raw=i,t.push(a),a}function Mh(t,e){z_[t]=e}function Ih(t){return Ay.extend(t)}function Th(t){return mx.extend(t)}function Ch(t){return vx.extend(t)}function Dh(t){return sl.extend(t)}function Ah(t){i("createCanvas",t)}function kh(t,e,i){t_.registerMap(t,e,i)}function Ph(t){var e=t_.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function Lh(t){return t}function Oh(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||Lh,this._newKeyGetter=n||Lh,this.context=r}function Eh(t,e,i,n,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[n](t[a],a),s=e[o];null==s?(i.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}function zh(t){var e={},i=e.encode={},n=N(),r=[],a=[],o=e.userOutput={dimensionNames:t.dimensions.slice(),encode:{}};f(t.dimensions,function(e){var s=t.getDimensionInfo(e),l=s.coordDim;if(l){var h=s.coordDimIndex;Bh(i,l)[h]=e,s.isExtraCoord||(n.set(l,1),Nh(s.type)&&(r[0]=e),Bh(o.encode,l)[h]=s.index),s.defaultTooltip&&a.push(e)}G_.each(function(t,e){var n=Bh(i,e),r=s.otherDims[e];null!=r&&r!==!1&&(n[r]=s.name)})});var s=[],l={};n.each(function(t,e){var n=i[e];l[e]=n[0],s=s.concat(n)}),e.dataDimsOnCoord=s,e.encodeFirstDimNotExtra=l;var h=i.label;h&&h.length&&(r=h.slice());var u=i.tooltip;return u&&u.length?a=u.slice():a.length||(a=r.slice()),i.defaultedLabel=r,i.defaultedTooltip=a,e}function Bh(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Rh(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function Nh(t){return!("ordinal"===t||"time"===t)}function Fh(t){null!=t&&o(this,t),this.otherDims={}}function Vh(t){return t._rawCount>65535?Z_:$_}function Hh(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function Wh(t,e){f(Q_.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,f(J_,function(i){t[i]=n(e[i])}),t._calculationInfo=o(e._calculationInfo)}function Gh(t,e,i,n,r){var a=q_[e.type],o=n-1,s=e.name,l=t[s][o];if(l&&l.length<i){for(var h=new a(Math.min(r-o*i,i)),u=0;u<l.length;u++)h[u]=l[u];t[s][o]=h}for(var c=n*i;r>c;c+=i)t[s].push(new a(Math.min(r-c,i)))}function Xh(t){var e=t._invertedIndicesMap;f(e,function(i,n){var r=t._dimensionInfos[n],a=r.ordinalMeta;if(a){i=e[n]=new K_(a.categories.length);for(var o=0;o<i.length;o++)i[o]=U_;for(var o=0;o<t._count;o++)i[t.get(n,o)]=o}})}function Yh(t,e,i){var n;if(null!=e){var r=t._chunkSize,a=Math.floor(i/r),o=i%r,s=t.dimensions[e],l=t._storage[s][a];if(l){n=l[o];var h=t._dimensionInfos[s].ordinalMeta;h&&h.categories.length&&(n=h.categories[n])}}return n}function Uh(t){return t}function jh(t){return t<this._count&&t>=0?this._indices[t]:-1}function qh(t,e){var i=t._idList[e];return null==i&&(i=Yh(t,t._idDimIdx,e)),null==i&&(i=j_+e),i}function Zh(t){return _(t)||(t=[t]),t}function Kh(t,e){var i=t.dimensions,n=new tw(p(i,t.getDimensionInfo,t),t.hostModel);Wh(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i.length;o++){var s=i[o];a[s]&&(h(e,s)>=0?(r[s]=$h(a[s]),n._rawExtent[s]=Qh(),n._extent[s]=null):r[s]=a[s])}return n}function $h(t){for(var e=new Array(t.length),i=0;i<t.length;i++)e[i]=Hh(t[i]);return e}function Qh(){return[1/0,-1/0]}function Jh(t,e,i){function r(t,e,i){null!=G_.get(e)?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,h.set(e,!0))}Jo.isInstance(e)||(e=Jo.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var a=(i.dimsDef||[]).slice(),l=N(),h=N(),u=[],c=tu(e,t,a,i.dimCount),d=0;c>d;d++){var p=a[d]=o({},S(a[d])?a[d]:{name:a[d]}),g=p.name,v=u[d]=new Fh;null!=g&&null==l.get(g)&&(v.name=v.displayName=g,l.set(g,d)),null!=p.type&&(v.type=p.type),null!=p.displayName&&(v.displayName=p.displayName)}var m=i.encodeDef;!m&&i.encodeDefaulter&&(m=i.encodeDefaulter(e,c)),m=N(m),m.each(function(t,e){if(t=$n(t).slice(),1===t.length&&!b(t[0])&&t[0]<0)return void m.set(e,!1);var i=m.set(e,[]);f(t,function(t,n){b(t)&&(t=l.get(t)),null!=t&&c>t&&(i[n]=t,r(u[t],e,n))})});var y=0;f(t,function(t){var e,t,i,a;if(b(t))e=t,t={};else{e=t.name;var o=t.ordinalMeta;t.ordinalMeta=null,t=n(t),t.ordinalMeta=o,i=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var l=m.get(e);if(l!==!1){var l=$n(l);if(!l.length)for(var h=0;h<(i&&i.length||1);h++){for(;y<u.length&&null!=u[y].coordDim;)y++;y<u.length&&l.push(y++)}f(l,function(n,o){var l=u[n];if(r(s(l,t),e,o),null==l.name&&i){var h=i[o];!S(h)&&(h={name:h}),l.name=l.displayName=h.name,l.defaultTooltip=h.defaultTooltip}a&&s(l.otherDims,a)})}});var x=i.generateCoord,_=i.generateCoordCount,w=null!=_;_=x?_||1:0;for(var M=x||"value",I=0;c>I;I++){var v=u[I]=u[I]||new Fh,T=v.coordDim;null==T&&(v.coordDim=eu(M,h,w),v.coordDimIndex=0,(!x||0>=_)&&(v.isExtraCoord=!0),_--),null==v.name&&(v.name=eu(v.coordDim,l)),null!=v.type||cs(e,I,v.name)!==Wy.Must&&(!v.isExtraCoord||null==v.otherDims.itemName&&null==v.otherDims.seriesName)||(v.type="ordinal")}return u}function tu(t,e,i,n){var r=Math.max(t.dimensionsDetectCount||1,e.length,i.length,n||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function eu(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}function iu(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=N(),this.categoryAxisMap=N(),this.firstCategoryDimIndex=null}function nu(t){var e=t.get("coordinateSystem"),i=new iu(e),n=rw[e];return n?(n(t,i,i.axisMap,i.categoryAxisMap),i):void 0}function ru(t){return"category"===t.get("type")}function au(t,e,i){i=i||{};var n,r,a,o,s=i.byIndex,l=i.stackedCoordDimension,h=!(!t||!t.get("stack"));if(f(e,function(t,i){b(t)&&(e[i]=t={name:t}),h&&!t.isExtraCoord&&(s||n||!t.ordinalMeta||(n=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||n||(s=!0),r){a="__\x00ecstackresult",o="__\x00ecstackedover",n&&(n.createInvertedIndices=!0);var u=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===u&&d++}),e.push({name:a,coordDim:u,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:o,coordDim:o,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:n&&n.name,isStackedByIndex:s,stackedOverDimension:o,stackResultDimension:a}}function ou(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function su(t,e){return ou(t,e)?t.getCalculationInfo("stackResultDimension"):e}function lu(t,e,i){i=i||{},Jo.isInstance(t)||(t=Jo.seriesDataToSource(t));var n,r=e.get("coordinateSystem"),a=ws.get(r),o=nu(e);o&&(n=p(o.coordSysDims,function(t){var e={name:t},i=o.axisMap.get(t);if(i){var n=i.get("type");e.type=Rh(n)}return e})),n||(n=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"]);var s,l,h=nw(t,{coordDimensions:n,generateCoord:i.generateCoord,encodeDefaulter:i.useEncodeDefaulter?x(ls,n,e):null});o&&f(h,function(t,e){var i=t.coordDim,n=o.categoryAxisMap.get(i);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=au(e,h),c=new tw(h,e);c.setCalculationInfo(u);var d=null!=s&&hu(t)?function(t,e,i,n){return n===s?i:this.defaultDimValueGetter(t,e,i,n)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function hu(t){if(t.sourceFormat===Ey){var e=uu(t.data||[]);return null!=e&&!_(Jn(e))}}function uu(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function cu(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function du(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function fu(t){return t._map||(t._map=N(t.categories))}function pu(t){return S(t)&&null!=t.value?t.value:t+""}function gu(t,e,i,n){var r={},a=t[1]-t[0],o=r.interval=Po(a/e,!0);null!=i&&i>o&&(o=r.interval=i),null!=n&&o>n&&(o=r.interval=n);var s=r.intervalPrecision=vu(o),l=r.niceTickExtent=[lw(Math.ceil(t[0]/o)*o,s),lw(Math.floor(t[1]/o)*o,s)];return yu(l,t),r}function vu(t){return So(t)+2}function mu(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function yu(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),mu(t,0,e),mu(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function xu(t){return t.get("stack")||cw+t.seriesIndex}function _u(t){return t.dim+t.index}function wu(t,e){var i=[];return e.eachSeriesByType(t,function(t){Cu(t)&&!Du(t)&&i.push(t)}),i}function bu(t){var e={};f(t,function(t){var i=t.coordinateSystem,n=i.getBaseAxis();if("time"===n.type||"value"===n.type)for(var r=t.getData(),a=n.dim+"_"+n.index,o=r.mapDimension(n.dim),s=0,l=r.count();l>s;++s){var h=r.get(o,s);e[a]?e[a].push(h):e[a]=[h]}});var i=[];for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(r){r.sort(function(t,e){return t-e});for(var a=null,o=1;o<r.length;++o){var s=r[o]-r[o-1];s>0&&(a=null===a?s:Math.min(a,s))}i[n]=a}}return i}function Su(t){var e=bu(t),i=[];return f(t,function(t){var n,r=t.coordinateSystem,a=r.getBaseAxis(),o=a.getExtent();if("category"===a.type)n=a.getBandWidth();else if("value"===a.type||"time"===a.type){var s=a.dim+"_"+a.index,l=e[s],h=Math.abs(o[1]-o[0]),u=a.scale.getExtent(),c=Math.abs(u[1]-u[0]);n=l?h/c*l:h}else{var d=t.getData();n=Math.abs(o[1]-o[0])/d.count()}var f=xo(t.get("barWidth"),n),p=xo(t.get("barMaxWidth"),n),g=xo(t.get("barMinWidth")||1,n),v=t.get("barGap"),m=t.get("barCategoryGap");i.push({bandWidth:n,barWidth:f,barMaxWidth:p,barMinWidth:g,barGap:v,barCategoryGap:m,axisKey:_u(a),stackId:xu(t)})}),Mu(i)}function Mu(t){var e={};f(t,function(t){var i=t.axisKey,n=t.bandWidth,r=e[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=r.stacks;e[i]=r;var o=t.stackId;a[o]||r.autoWidthCount++,a[o]=a[o]||{width:0,maxWidth:0};var s=t.barWidth;s&&!a[o].width&&(a[o].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(a[o].maxWidth=l);var h=t.barMinWidth;h&&(a[o].minWidth=h);var u=t.barGap;null!=u&&(r.gap=u);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var i={};return f(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=xo(t.categoryGap,r),o=xo(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-a)/(l+(l-1)*o);h=Math.max(h,0),f(n,function(t){var e=t.maxWidth,i=t.minWidth;if(t.width){var n=t.width;e&&(n=Math.min(n,e)),i&&(n=Math.max(n,i)),t.width=n,s-=n+o*n,l--}else{var n=h;e&&n>e&&(n=Math.min(e,s)),i&&i>n&&(n=i),n!==h&&(t.width=n,s-=n+o*n,l--)}}),h=(s-a)/(l+(l-1)*o),h=Math.max(h,0);var u,c=0;f(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+o)}),u&&(c-=u.width*o);var d=-c/2;f(n,function(t,n){i[e][n]=i[e][n]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+o)})}),i}function Iu(t,e,i){if(t&&e){var n=t[_u(e)];return null!=n&&null!=i&&(n=n[xu(i)]),n}}function Tu(t,e){var i=wu(t,e),n=Su(i),r={};f(i,function(t){var e=t.getData(),i=t.coordinateSystem,a=i.getBaseAxis(),o=xu(t),s=n[_u(a)][o],l=s.offset,h=s.width,u=i.getOtherAxis(a),c=t.get("barMinHeight")||0;r[o]=r[o]||[],e.setLayout({bandWidth:s.bandWidth,offset:l,size:h});for(var d=e.mapDimension(u.dim),f=e.mapDimension(a.dim),p=ou(e,d),g=u.isHorizontal(),v=Au(a,u,p),m=0,y=e.count();y>m;m++){var x=e.get(d,m),_=e.get(f,m);if(!isNaN(x)&&!isNaN(_)){var w=x>=0?"p":"n",b=v;p&&(r[o][_]||(r[o][_]={p:v,n:v}),b=r[o][_][w]);var S,M,I,T;if(g){var C=i.dataToPoint([x,_]);S=b,M=C[1]+l,I=C[0]-v,T=h,Math.abs(I)<c&&(I=(0>I?-1:1)*c),p&&(r[o][_][w]+=I)}else{var C=i.dataToPoint([_,x]);S=C[0]+l,M=b,I=h,T=C[1]-v,Math.abs(T)<c&&(T=(0>=T?-1:1)*c),p&&(r[o][_][w]+=T)}e.setItemLayout(m,{x:S,y:M,width:I,height:T})}}},this)}function Cu(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Du(t){return t.pipelineContext&&t.pipelineContext.large}function Au(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}function ku(t,e){return Dw(t,Cw(e))}function Pu(t,e){var i,n,r,a=t.type,o=e.getMin(),s=e.getMax(),l=null!=o,h=null!=s,u=t.getExtent();"ordinal"===a?i=e.getCategories().length:(n=e.get("boundaryGap"),_(n)||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(n=[0,0]),n[0]=xo(n[0],1),n[1]=xo(n[1],1),r=u[1]-u[0]||Math.abs(u[0])),null==o&&(o="ordinal"===a?i?0:0/0:u[0]-n[0]*r),null==s&&(s="ordinal"===a?i?i-1:0/0:u[1]+n[1]*r),"dataMin"===o?o=u[0]:"function"==typeof o&&(o=o({min:u[0],max:u[1]})),"dataMax"===s?s=u[1]:"function"==typeof s&&(s=s({min:u[0],max:u[1]})),(null==o||!isFinite(o))&&(o=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(C(o)||C(s)||"ordinal"===a&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(o>0&&s>0&&!l&&(o=0),0>o&&0>s&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===a){var d,p=wu("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=Su(p),v=Lu(o,s,e,g);o=v.min,s=v.max}}return[o,s]}function Lu(t,e,i,n){var r=i.axis.getExtent(),a=r[1]-r[0],o=Iu(n,i.axis);if(void 0===o)return{min:t,max:e};var s=1/0;f(o,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(o,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=1-(s+l)/a,d=u/c-u;return e+=d*(l/h),t-=d*(s/h),{min:t,max:e}}function Ou(t,e){var i=Pu(t,e),n=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:a,fixMin:n,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function Eu(t,e){if(e=e||t.get("type"))switch(e){case"category":return new sw(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new uw;default:return(cu.getClass(e)||uw).create(t)}}function zu(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)}function Bu(t){var e=t.getLabelModel().get("formatter"),i="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(i){return i=t.scale.getLabel(i),e.replace("{value}",null!=i?i:"")}}(e):"function"==typeof e?function(n,r){return null!=i&&(r=n-i),e(Ru(t,n),r)}:function(e){return t.scale.getLabel(e)}}function Ru(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Nu(t){var e=t.model,i=t.scale;if(e.get("axisLabel.show")&&!i.isBlank()){var n,r,a="category"===t.type,o=i.getExtent();a?r=i.count():(n=i.getTicks(),r=n.length);var s,l=t.getLabelModel(),h=Bu(t),u=1;r>40&&(u=Math.ceil(r/40));for(var c=0;r>c;c+=u){var d=n?n[c]:o[0]+c,f=h(d),p=l.getTextRect(f),g=Fu(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function Fu(t,e){var i=e*Math.PI/180,n=t.plain(),r=n.width,a=n.height,o=r*Math.cos(i)+a*Math.sin(i),s=r*Math.sin(i)+a*Math.cos(i),l=new bi(n.x,n.y,o,s);return l}function Vu(t){var e=t.get("interval");return null==e?"auto":e}function Hu(t){return"category"===t.type&&0===Vu(t.getLabelModel())}function Wu(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function Gu(t,e,i,n,r,a,o){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?ma(t.slice(8),new bi(e,i,n,r),o?"center":"cover"):0===t.indexOf("path://")?va(t.slice(7),{},new bi(e,i,n,r),o?"center":"cover"):new Ww({shape:{symbolType:t,x:e,y:i,width:n,height:r}}),l.__isEmptyBrush=s,l.setColor=Wu,l.setColor(a),l}function Xu(t){return lu(t.getSource(),t)}function Yu(t,e){var i=e;uo.isInstance(e)||(i=new uo(e),c(i,Ew));var n=Eu(i);return n.setExtent(t[0],t[1]),Ou(n,i),n}function Uu(t){c(t,Ew)}function ju(t,e){return Math.abs(t-e)<Yw}function qu(t,e,i){var n=0,r=t[0];if(!r)return!1;for(var a=1;a<t.length;a++){var o=t[a];n+=Gr(r[0],r[1],o[0],o[1],e,i),r=o}var s=t[0];return ju(r[0],s[0])&&ju(r[1],s[1])||(n+=Gr(r[0],r[1],s[0],s[1],e,i)),0!==n}function Zu(t,e,i){if(this.name=t,this.geometries=e,i)i=[i[0],i[1]];else{var n=this.getBoundingRect();i=[n.x+n.width/2,n.y+n.height/2]}this.center=i}function Ku(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var i=t.features,n=0;n<i.length;n++)for(var r=i[n],a=r.geometry,o=a.coordinates,s=a.encodeOffsets,l=0;l<o.length;l++){var h=o[l];if("Polygon"===a.type)o[l]=$u(h,s[l],e);else if("MultiPolygon"===a.type)for(var u=0;u<h.length;u++){var c=h[u];h[u]=$u(c,s[l][u],e)}}return t.UTF8Encoding=!1,t}function $u(t,e,i){for(var n=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,n.push([s/i,l/i])}return n}function Qu(t){return"category"===t.type?tc(t):nc(t)}function Ju(t,e){return"category"===t.type?ic(t,e):{ticks:t.scale.getTicks()}}function tc(t){var e=t.getLabelModel(),i=ec(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:i.labelCategoryInterval}:i}function ec(t,e){var i=rc(t,"labels"),n=Vu(e),r=ac(i,n);if(r)return r;var a,o;return w(n)?a=cc(t,n):(o="auto"===n?sc(t):n,a=uc(t,o)),oc(i,n,{labels:a,labelCategoryInterval:o})}function ic(t,e){var i=rc(t,"ticks"),n=Vu(e),r=ac(i,n);if(r)return r;var a,o;if((!e.get("show")||t.scale.isBlank())&&(a=[]),w(n))a=cc(t,n,!0);else if("auto"===n){var s=ec(t,t.getLabelModel());o=s.labelCategoryInterval,a=p(s.labels,function(t){return t.tickValue})}else o=n,a=uc(t,o,!0);return oc(i,n,{ticks:a,tickCategoryInterval:o})}function nc(t){var e=t.scale.getTicks(),i=Bu(t);return{labels:p(e,function(e,n){return{formattedLabel:i(e,n),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function rc(t,e){return jw(t)[e]||(jw(t)[e]=[])}function ac(t,e){for(var i=0;i<t.length;i++)if(t[i].key===e)return t[i].value}function oc(t,e,i){return t.push({key:e,value:i}),i}function sc(t){var e=jw(t).autoInterval;return null!=e?e:jw(t).autoInterval=t.calculateCategoryInterval()}function lc(t){var e=hc(t),i=Bu(t),n=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,a=r.getExtent(),o=r.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],h=t.dataToCoord(l+1)-t.dataToCoord(l),u=Math.abs(h*Math.cos(n)),c=Math.abs(h*Math.sin(n)),d=0,f=0;l<=a[1];l+=s){var p=0,g=0,v=Wi(i(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/u,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var x=Math.max(0,Math.floor(Math.min(m,y))),_=jw(t.model),w=t.getExtent(),b=_.lastAutoInterval,S=_.lastTickCount;return null!=b&&null!=S&&Math.abs(b-x)<=1&&Math.abs(S-o)<=1&&b>x&&_.axisExtend0===w[0]&&_.axisExtend1===w[1]?x=b:(_.lastTickCount=o,_.lastAutoInterval=x,_.axisExtend0=w[0],_.axisExtend1=w[1]),x}function hc(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function uc(t,e,i){function n(t){l.push(i?t:{formattedLabel:r(t),rawLabel:a.getLabel(t),tickValue:t})}var r=Bu(t),a=t.scale,o=a.getExtent(),s=t.getLabelModel(),l=[],h=Math.max((e||0)+1,1),u=o[0],c=a.count();0!==u&&h>1&&c/h>2&&(u=Math.round(Math.ceil(u/h)*h));var d=Hu(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&u!==o[0]&&n(o[0]);for(var g=u;g<=o[1];g+=h)n(g);return p&&g-h!==o[1]&&n(o[1]),l}function cc(t,e,i){var n=t.scale,r=Bu(t),a=[];return f(n.getTicks(),function(t){var o=n.getLabel(t);e(t,o)&&a.push(i?t:{formattedLabel:r(t),rawLabel:o,tickValue:t})}),a}function dc(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}function fc(t,e,i,n){function r(t,e){return t=_o(t),e=_o(e),d?t>e:e>t}var a=e.length;if(t.onBand&&!i&&a){var o,s,l=t.getExtent();if(1===a)e[0].coord=l[0],o=e[1]={coord:l[0]};else{var h=e[a-1].tickValue-e[0].tickValue,u=(e[a-1].coord-e[0].coord)/h;f(e,function(t){t.coord-=u/2});var c=t.scale.getExtent();s=1+c[1]-e[a-1].tickValue,o={coord:e[a-1].coord+u*s},e.push(o)}var d=l[0]>l[1];r(e[0].coord,l[0])&&(n?e[0].coord=l[0]:e.shift()),n&&r(l[0],e[0].coord)&&e.unshift({coord:l[0]}),r(l[1],o.coord)&&(n?o.coord=l[1]:e.pop()),n&&r(o.coord,l[1])&&e.push({coord:l[1]})}}function pc(t){return this._axes[t]}function gc(t){Jw.call(this,t)}function vc(t,e){return e.type||(e.data?"category":"value")}function mc(t,e){return t.getCoordSysModel()===e}function yc(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}function xc(t,e,i,n){function r(t){return t.dim+"_"+t.index}i.getAxesOnZeroOf=function(){return a?[a]:[]};var a,o=t[e],s=i.model,l=s.get("axisLine.onZero"),h=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=h)_c(o[h])&&(a=o[h]);else for(var u in o)if(o.hasOwnProperty(u)&&_c(o[u])&&!n[r(o[u])]){a=o[u];break}a&&(n[r(a)]=!0)}}function _c(t){return t&&"category"!==t.type&&"time"!==t.type&&zu(t)}function wc(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function bc(t){return p(lb,function(e){var i=t.getReferringComponents(e)[0];return i})}function Sc(t){return"cartesian2d"===t.get("coordinateSystem")}function Mc(t,e){var i=t.mapDimension("defaultedLabel",!0),n=i.length;if(1===n)return js(t,e,i[0]);if(n){for(var r=[],a=0;a<i.length;a++){var o=js(t,e,i[a]);r.push(o)}return r.join(" ")}}function Ic(t,e,i,n,r,a){var o=i.getModel("label"),s=i.getModel("emphasis.label");Fa(t,e,o,s,{labelFetcher:r,labelDataIndex:a,defaultText:Mc(r.getData(),a),isRectText:!0,autoColor:n}),Tc(t),Tc(e)}function Tc(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function Cc(t,e,i){var n=t.getArea(),r=t.getBaseAxis().isHorizontal(),a=n.x,o=n.y,s=n.width,l=n.height,h=i.get("lineStyle.width")||2;a-=h/2,o-=h/2,s+=h,l+=h;var u=new Om({shape:{x:a,y:o,width:s,height:l}});return e&&(u.shape[r?"width":"height"]=0,Qa(u,{shape:{width:s,height:l}},i)),u}function Dc(t,e,i){var n=t.getArea(),r=new Im({shape:{cx:_o(t.cx,1),cy:_o(t.cy,1),r0:_o(n.r0,1),r:_o(n.r,1),startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}});return e&&(r.shape.endAngle=n.startAngle,Qa(r,{shape:{endAngle:n.endAngle}},i)),r}function Ac(t,e,i){return t?"polar"===t.type?Dc(t,e,i):"cartesian2d"===t.type?Cc(t,e,i):null:null}function kc(t,e){var i=t.getArea&&t.getArea();if("cartesian2d"===t.type){var n=t.getBaseAxis();if("category"!==n.type||!n.onBand){var r=e.getLayout("bandWidth");n.isHorizontal()?(i.x-=r,i.width+=2*r):(i.y-=r,i.height+=2*r)}}return i}function Pc(t,e,i){i.style.text=null,$a(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function Lc(t,e,i){i.style.text=null,$a(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}function Oc(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function Ec(t,e,i,n,r,a,o,l){var h=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),c=e.getVisual("borderColor"),d=n.getModel("itemStyle"),f=n.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",d.get("barBorderRadius")||0),t.useStyle(s({stroke:Oc(r)?"none":c,fill:Oc(r)?"none":h,opacity:u},d.getBarItemStyle()));var p=n.getShallow("cursor");p&&t.attr("cursor",p);var g=o?r.height>0?"bottom":"top":r.width>0?"left":"right";l||Ic(t.style,f,n,h,a,i,g),Oc(r)&&(f.fill=f.stroke="none"),za(t,f)}function zc(t,e){var i=t.get(fb)||0;return Math.min(i,Math.abs(e.width),Math.abs(e.height))}function Bc(t,e,i){var n=t.getData(),r=[],a=n.getLayout("valueAxisHorizontal")?1:0;r[1-a]=n.getLayout("valueAxisStart");var o=new _b({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:r,__baseDimIdx:a,__largeDataIndices:n.getLayout("largeDataIndices"),__barWidth:n.getLayout("barWidth")});e.add(o),Nc(o,t,n),o.seriesIndex=t.seriesIndex,t.get("silent")||(o.on("mousedown",wb),o.on("mousemove",wb))}function Rc(t,e,i){var n=t.__baseDimIdx,r=1-n,a=t.shape.points,o=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];pb[0]=e,pb[1]=i;for(var h=pb[n],u=pb[1-n],c=h-s,d=h+s,f=0,p=a.length/2;p>f;f++){var g=2*f,v=a[g+n],m=a[g+r];if(v>=c&&d>=v&&(m>=l?u>=l&&m>=u:u>=m&&l>=u))return o[f]}return-1}function Nc(t,e,i){var n=i.getVisual("borderColor")||i.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}function Fc(t,e,i,n){var r,a,o=To(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return Co(o-bb/2)?(a=l?"bottom":"top",r="center"):Co(o-1.5*bb)?(a=l?"top":"bottom",r="center"):(a="middle",r=1.5*bb>o&&o>bb/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function Vc(t,e,i){if(!Hu(t.axis)){var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],i=i||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],h=i[0],u=i[1],c=i[i.length-1],d=i[i.length-2];n===!1?(Hc(a),Hc(h)):Wc(a,o)&&(n?(Hc(o),Hc(u)):(Hc(a),Hc(h))),r===!1?(Hc(s),Hc(c)):Wc(l,s)&&(r?(Hc(l),Hc(d)):(Hc(s),Hc(c)))}}function Hc(t){t&&(t.ignore=!0)}function Wc(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=ke([]);return Ee(r,r,-t.rotation),i.applyTransform(Le([],r,t.getLocalTransform())),n.applyTransform(Le([],r,e.getLocalTransform())),i.intersect(n)}}function Gc(t){return"middle"===t||"center"===t}function Xc(t,e,i,n,r){for(var a=[],o=[],s=[],l=0;l<t.length;l++){var h=t[l].coord;o[0]=h,o[1]=0,s[0]=h,s[1]=i,e&&(ae(o,o,e),ae(s,s,e));var u=new zm({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:n,z2:2,silent:!0});a.push(u)}return a}function Yc(t,e,i){var n=e.axis,r=e.getModel("axisTick");if(r.get("show")&&!n.scale.isBlank()){for(var a=r.getModel("lineStyle"),o=i.tickDirection*r.get("length"),l=n.getTicksCoords(),h=Xc(l,t._transform,o,s(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),u=0;u<h.length;u++)t.group.add(h[u]);return h}}function Uc(t,e,i){var n=e.axis,r=e.getModel("minorTick");if(r.get("show")&&!n.scale.isBlank()){var a=n.getMinorTicksCoords();if(a.length)for(var o=r.getModel("lineStyle"),l=i.tickDirection*r.get("length"),h=s(o.getLineStyle(),s(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),u=0;u<a.length;u++)for(var c=Xc(a[u],t._transform,l,h,"minorticks_"+u),d=0;d<c.length;d++)t.group.add(c[d])}}function jc(t,e,i){var n=e.axis,r=D(i.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var a=e.getModel("axisLabel"),o=a.get("margin"),s=n.getViewLabels(),l=(D(i.labelRotate,a.get("rotate"))||0)*bb/180,h=Tb(i.rotation,l,i.labelDirection),u=e.getCategories&&e.getCategories(!0),c=[],d=Cb(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=a;u&&u[l]&&u[l].textStyle&&(v=new uo(u[l].textStyle,a,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=n.dataToCoord(l),x=[y,i.labelOffset+i.labelDirection*o],_=new wm({anid:"label_"+l,position:x,rotation:h.rotation,silent:d,z2:10});Ha(_.style,v,{text:f,textAlign:v.getShallow("align",!0)||h.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,textFill:"function"==typeof m?m("category"===n.type?g:"value"===n.type?l+"":l,s):m}),p&&(_.eventData=Ib(e),_.eventData.targetType="axisLabel",_.eventData.value=g),t._dumbGroup.add(_),_.updateTransform(),c.push(_),t.group.add(_),_.decomposeTransform()}),c}}function qc(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return Zc(i,t,e),i.seriesInvolved&&$c(i,t),i}function Zc(t,e,i){var n=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),a=r.get("link",!0)||[],o=[];Db(i.getCoordinateSystems(),function(i){function s(n,s,l){var u=l.model.getModel("axisPointer",r),d=u.get("show");if(d&&("auto"!==d||n||nd(u))){null==s&&(s=u.get("triggerTooltip")),u=n?Kc(l,c,r,e,n,s):u;var f=u.get("snap"),p=rd(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:i,axisPointerModel:u,triggerTooltip:s,involveSeries:g,snap:f,useHandle:nd(u),seriesModels:[]};h[p]=v,t.seriesInvolved|=g;var m=Qc(a,l);if(null!=m){var y=o[m]||(o[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=a[m].mapper,v.linkGroup=y}}}if(i.axisPointerEnabled){var l=rd(i.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=i;var u=i.model,c=u.getModel("tooltip",n);if(Db(i.getAxes(),Ab(s,!1,null)),i.getTooltipAxes&&n&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=i.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&Db(p.baseAxes,Ab(s,f?"cross":!0,d)),f&&Db(p.otherAxes,Ab(s,"cross",!1))}}})}function Kc(t,e,i,r,a,o){var l=e.getModel("axisPointer"),h={};Db(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=n(l.get(t))}),h.snap="category"!==t.type&&!!o,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===a){var c=l.get("label.show");if(u.show=null!=c?c:!0,!o){var d=h.lineStyle=l.get("crossStyle");d&&s(u,d.textStyle)}}return t.model.getModel("axisPointer",new uo(h,i,r))}function $c(t,e){e.eachSeries(function(e){var i=e.coordinateSystem,n=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);i&&"none"!==n&&n!==!1&&"item"!==n&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&Db(t.coordSysAxesInfo[rd(i.model)],function(t){var n=t.axis;i.getAxis(n.dim)===n&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function Qc(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(Jc(a[n+"AxisId"],i.id)||Jc(a[n+"AxisIndex"],i.componentIndex)||Jc(a[n+"AxisName"],i.name))return r}}function Jc(t,e){return"all"===t||_(t)&&h(t,e)>=0||t===e}function td(t){var e=ed(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,a=i.get("status"),o=i.get("value");null!=o&&(o=n.parse(o));var s=nd(i);null==a&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function ed(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[rd(t)]}function id(t){var e=ed(t);return e&&e.axisPointerModel}function nd(t){return!!t.get("handle.show")}function rd(t){return t.type+"||"+t.id}function ad(t,e,i,n,r,a){var o=kb.getAxisPointerClass(t.axisPointerClass);if(o){var s=id(e);s?(t._axisPointer||(t._axisPointer=new o)).render(e,s,n,a):od(t,n)}}function od(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}function sd(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,a={},o=r.getAxesOnZeroOf()[0],s=r.position,l=o?"onZero":s,h=r.dim,u=n.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===h?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}a.position=["y"===h?p[d[l]]:c[0],"x"===h?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===h?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),D(i.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-m:m,a.z2=1,a}function ld(t,e,i){og.call(this),this.updateData(t,e,i)}function hd(t){return[t[0]/2,t[1]/2]}function ud(t,e){this.parent.drift(t,e)}function cd(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var i=this.__symbolOriginalScale,n=i[1]/i[0],r={scale:[Math.max(1.1*i[0],i[0]+3),Math.max(1.1*i[1],i[1]+3*n)]};
this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function dd(t){this.group=new og,this._symbolCtor=t||ld}function fd(t,e,i,n){return!(!e||isNaN(e[0])||isNaN(e[1])||n.isIgnore&&n.isIgnore(i)||n.clipShape&&!n.clipShape.contain(e[0],e[1])||"none"===t.getItemVisual(i,"symbol"))}function pd(t){return null==t||S(t)||(t={isIgnore:t}),t||{}}function gd(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function vd(t,e,i){var n,r=t.getBaseAxis(),a=t.getOtherAxis(r),o=md(a,i),s=r.dim,l=a.dim,h=e.mapDimension(l),u=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=p(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(n|=ou(e,d[0]))&&(d[0]=f),(n|=ou(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:o,valueAxisDim:l,baseAxisDim:s,stacked:!!n,valueDim:h,baseDim:u,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function md(t,e){var i=0,n=t.scale.getExtent();return"start"===e?i=n[0]:"end"===e?i=n[1]:n[0]>0?i=n[0]:n[1]<0&&(i=n[1]),i}function yd(t,e,i,n){var r=0/0;t.stacked&&(r=i.get(i.getCalculationInfo("stackedOverDimension"),n)),isNaN(r)&&(r=t.valueStart);var a=t.baseDataOffset,o=[];return o[a]=i.get(t.baseDim,n),o[1-a]=r,e.dataToPoint(o)}function xd(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})}).update(function(t,e){i.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){i.push({cmd:"-",idx:t})}).execute(),i}function _d(t){return isNaN(t[0])||isNaN(t[1])}function wd(t,e,i,n,r,a,o,s,l,h){return"none"!==h&&h?bd.apply(this,arguments):Sd.apply(this,arguments)}function bd(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(_d(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]);else if(l>0){var g=e[c],v="y"===h?1:0,m=(p[v]-g[v])*l;Ub(qb,g),qb[v]=g[v]+m,Ub(Zb,p),Zb[v]=p[v]-m,t.bezierCurveTo(qb[0],qb[1],Zb[0],Zb[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function Sd(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(_d(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]),Ub(qb,p);else if(l>0){var g=d+a,v=e[g];if(u)for(;v&&_d(e[g]);)g+=a,v=e[g];var m=.5,y=e[c],v=e[g];if(!v||_d(v))Ub(Zb,p);else{_d(v)&&!u&&(v=p),j(jb,v,y);var x,_;if("x"===h||"y"===h){var w="x"===h?0:1;x=Math.abs(p[w]-y[w]),_=Math.abs(p[w]-v[w])}else x=cp(p,y),_=cp(p,v);m=_/(_+x),Yb(Zb,p,jb,-l*(1-m))}Gb(qb,qb,s),Xb(qb,qb,o),Gb(Zb,Zb,s),Xb(Zb,Zb,o),t.bezierCurveTo(qb[0],qb[1],Zb[0],Zb[1],p[0],p[1]),Yb(qb,p,jb,l*m)}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function Md(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}function Id(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function Td(t){return"number"==typeof t?t:t?.5:0}function Cd(t,e,i){if(!i.valueDim)return[];for(var n=[],r=0,a=e.count();a>r;r++)n.push(yd(i,t,e,r));return n}function Dd(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var h=[];switch(i){case"end":h[r]=s[r],h[1-r]=l[1-r],a.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],a.push(h),a.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],a.push(h)}}return t[o]&&a.push(t[o]),a}function Ad(t,e){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var n,r,a=i.length-1;a>=0;a--){var o=i[a].dimension,s=t.dimensions[o],l=t.getDimensionInfo(s);if(n=l&&l.coordDim,"x"===n||"y"===n){r=i[a];break}}if(r){var h=e.getAxis(n),u=p(r.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),c=u.length,d=r.outerColors.slice();c&&u[0].coord>u[c-1].coord&&(u.reverse(),d.reverse());var g=10,v=u[0].coord-g,m=u[c-1].coord+g,y=m-v;if(.001>y)return"transparent";f(u,function(t){t.offset=(t.coord-v)/y}),u.push({offset:c?u[c-1].offset:.5,color:d[1]||"transparent"}),u.unshift({offset:c?u[0].offset:.5,color:d[0]||"transparent"});var x=new Hm(0,0,0,0,u,!0);return x[n]=v,x[n+"2"]=m,x}}}function kd(t,e,i){var n=t.get("showAllSymbol"),r="auto"===n;if(!n||r){var a=i.getAxesByScale("ordinal")[0];if(a&&(!r||!Pd(a,e))){var o=e.mapDimension(a.dim),s={};return f(a.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(o,t))}}}}function Pd(t,e){var i=t.getExtent(),n=Math.abs(i[1]-i[0])/t.scale.count();isNaN(n)&&(n=0);for(var r=e.count(),a=Math.max(1,Math.round(r/5)),o=0;r>o;o+=a)if(1.5*ld.getSymbolSize(e,o)[t.isHorizontal()?1:0]>n)return!1;return!0}function Ld(t,e,i){if("cartesian2d"===t.type){var n=t.getBaseAxis().isHorizontal(),r=Cc(t,e,i);if(!i.get("clip",!0)){var a=r.shape,o=Math.max(a.width,a.height);n?(a.y-=o,a.height+=2*o):(a.x-=o,a.width+=2*o)}return r}return Dc(t,e,i)}function Od(t,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){var i=e();return i.indexOfName(t)>=0},this.indexOfName=function(e){var i=t();return i.indexOfName(e)},this.getItemVisual=function(e,i){var n=t();return n.getItemVisual(e,i)}}function Ed(t,e,i,n){var r=e.getData(),a=this.dataIndex,o=r.getName(a),s=e.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:t,name:o,seriesId:e.id}),r.each(function(t){zd(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,i)})}function zd(t,e,i,n,r){var a=(e.startAngle+e.endAngle)/2,o=Math.cos(a),s=Math.sin(a),l=i?n:0,h=[o*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function Bd(t,e){og.call(this);var i=new Im({z2:2}),n=new km,r=new wm;this.add(i),this.add(n),this.add(r),this.updateData(t,e,!0)}function Rd(t,e,i,n,r,a,o,s,l,h){function u(e,i,n){for(var r=e;i>r&&!(t[r].y+n>l+o);r++)if(t[r].y+=n,r>e&&i>r+1&&t[r+1].y>t[r].y+t[r].height)return void c(r,n/2);c(i-1,n/2)}function c(e,i){for(var n=e;n>=0&&!(t[n].y-i<l)&&(t[n].y-=i,!(n>0&&t[n].y>t[n-1].y+t[n-1].height));n--);}function d(t,e,i,n,r,a){for(var o=a>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;l>s;s++)if("none"===t[s].labelAlignTo){var h=Math.abs(t[s].y-n),u=t[s].len,c=t[s].len2,d=r+u>h?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-i);e&&d>=o&&(d=o-10),!e&&o>=d&&(d=o+10),t[s].x=i+d*a,o=d}}t.sort(function(t,e){return t.y-e.y});for(var f,p=0,g=t.length,v=[],m=[],y=0;g>y;y++){if("outer"===t[y].position&&"labelLine"===t[y].labelAlignTo){var x=t[y].x-h;t[y].linePoints[1][0]+=x,t[y].x=h}f=t[y].y-p,0>f&&u(y,g,-f,r),p=t[y].y+t[y].height}0>o-p&&c(g-1,p-o);for(var y=0;g>y;y++)t[y].y>=i?m.push(t[y]):v.push(t[y]);d(v,!1,e,i,n,r),d(m,!0,e,i,n,r)}function Nd(t,e,i,n,r,a,o,s){for(var l=[],h=[],u=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)Fd(t[d])||(t[d].x<e?(u=Math.min(u,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),h.push(t[d])));Rd(h,e,i,n,1,r,a,o,s,c),Rd(l,e,i,n,-1,r,a,o,s,u);for(var d=0;d<t.length;d++){var f=t[d];if(!Fd(f)){var p=f.linePoints;if(p){var g,v="edge"===f.labelAlignTo,m=f.textRect.width;g=v?f.x<e?p[2][0]-f.labelDistance-o-f.labelMargin:o+r-f.labelMargin-p[2][0]-f.labelDistance:f.x<e?f.x-o-f.bleedMargin:o+r-f.x-f.bleedMargin,g<f.textRect.width&&(f.text=qi(f.text,g,f.font),"edge"===f.labelAlignTo&&(m=Hi(f.text,f.font)));var y=p[1][0]-p[2][0];v?p[2][0]=f.x<e?o+f.labelMargin+m+f.labelDistance:o+r-f.labelMargin-m-f.labelDistance:(p[2][0]=f.x<e?f.x+f.labelDistance:f.x-f.labelDistance,p[1][0]=p[2][0]+y),p[1][1]=p[2][1]=f.y}}}}function Fd(t){return"center"===t.position}function Vd(t,e){return jo(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Hd(t,e,i){var n,r={},a="toggleSelected"===t;return i.eachComponent("legend",function(i){a&&null!=n?i[n?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?i[t]():(i[t](e.name),n=i.isSelected(e.name));var o=i.getData();f(o,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&n:n}})}),"allSelect"===t||"inverseSelect"===t?{selected:r}:{name:e.name,selected:r}}function Wd(t,e){var i=vy(e.get("padding")),n=e.getItemStyle(["color","opacity"]);n.fill=e.get("backgroundColor");var t=new Om({shape:{x:t.x-i[3],y:t.y-i[0],width:t.width+i[1]+i[3],height:t.height+i[0]+i[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1});return t}function Gd(t,e,i,n,r,a){var o;return"line"!==e&&e.indexOf("empty")<0?(o=i.getItemStyle(),t.style.stroke=n,a||(o.stroke=r)):o=i.getItemStyle(["borderWidth","borderColor"]),t.setStyle(o)}function Xd(t,e,i,n){Ud(t,e,i,n),i.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),Yd(t,e,i,n)}function Yd(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:n})}function Ud(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:n})}function jd(t,e,i){var n=t.getOrient(),r=[1,1];r[n.index]=0,qo(e,i,{type:"box",ignoreSize:r})}function qd(t,e,i,n,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void i.showPointer(t,e);var s=Zd(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!n&&t.snap&&a.containData(h)&&null!=h&&(e=h),i.showPointer(t,e,l,r),i.showTooltip(t,s,h)}}function Zd(t,e){var i=e.axis,n=i.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return AS(e.seriesModels,function(e){var l,h,u=e.getData().mapDimension(n,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(u,t,i);h=c.dataIndices,l=c.nestestValue}else{if(h=e.getData().indicesOfNearest(u[0],t,"category"===i.type?.5:null),!h.length)return;l=e.getData().get(u[0],h[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);o>=f&&((o>f||d>=0&&0>s)&&(o=f,s=d,r=l,a.length=0),AS(h,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function Kd(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function $d(t,e,i,n){var r=i.payloadBatch,a=e.axis,o=a.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=rd(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Qd(t,e,i){var n=i.axesInfo=[];AS(e,function(e,i){var r=e.axisPointerModel.option,a=t[i];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&n.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Jd(t,e,i,n){if(rf(e)||!t.list.length)return void n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function tf(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",a=PS(n)[r]||{},o=PS(n)[r]={};AS(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&AS(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var s=[],l=[];f(a,function(t,e){!o[e]&&l.push(t)}),f(o,function(t,e){!a[e]&&s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function ef(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function nf(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function rf(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function af(t,e,i){if(!jf.node){var n=e.getZr();OS(n).records||(OS(n).records={}),of(n,e);var r=OS(n).records[t]||(OS(n).records[t]={});r.handler=i}}function of(t,e){function i(i,n){t.on(i,function(i){var r=uf(e);ES(OS(t).records,function(t){t&&n(t,i,r.dispatchAction)}),sf(r.pendings,e)})}OS(t).initialized||(OS(t).initialized=!0,i("click",x(hf,"click")),i("mousemove",x(hf,"mousemove")),i("globalout",lf))}function sf(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function lf(t,e,i){t.handler("leave",null,i)}function hf(t,e,i,n){e.handler(t,i,n)}function uf(t){var e={showTip:[],hideTip:[]},i=function(n){var r=e[n.type];r?r.push(n):(n.dispatchAction=i,t.dispatchAction(n))};return{dispatchAction:i,pendings:e}}function cf(t,e){if(!jf.node){var i=e.getZr(),n=(OS(i).records||{})[t];n&&(OS(i).records[t]=null)}}function df(){}function ff(t,e,i,n){pf(BS(i).lastProp,n)||(BS(i).lastProp=n,e?$a(i,n,t):(i.stopAnimation(),i.attr(n)))}function pf(t,e){if(S(t)&&S(e)){var i=!0;return f(e,function(e,n){i=i&&pf(t[n],e)}),!!i}return t===e}function gf(t,e){t[e.get("label.show")?"show":"hide"]()}function vf(t){return{position:t.position.slice(),rotation:t.rotation||0}}function mf(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function yf(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle(),e.fill=null):"shadow"===i&&(e=n.getAreaStyle(),e.stroke=null),e}function xf(t,e,i,n,r){var a=i.get("value"),o=wf(a,e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),s=i.getModel("label"),l=vy(s.get("padding")||0),h=s.getFont(),u=Wi(o,h),c=r.position,d=u.width+l[1]+l[3],f=u.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),_f(c,d,f,n);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:o,textFont:h,textFill:s.getTextColor(),textPosition:"inside",textPadding:l,fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function _f(t,e,i,n){var r=n.getWidth(),a=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,a)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function wf(t,e,i,n,r){t=e.scale.parse(t);var a=e.scale.getLabel(t,{precision:r.precision}),o=r.formatter;if(o){var s={value:Ru(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};f(n,function(t){var e=i.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,r=e&&e.getDataParams(n);r&&s.seriesData.push(r)}),b(o)?a=o.replace("{value}",a):w(o)&&(a=o(s))}return a}function bf(t,e,i){var n=Ae();return Ee(n,n,i.rotation),Oe(n,n,i.position),to([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}function Sf(t,e,i,n,r,a){var o=Sb.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),xf(e,n,r,a,{position:bf(n.axis,t,i),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function Mf(t,e,i){return i=i||0,{x1:t[i],y1:t[1-i],x2:e[i],y2:e[1-i]}}function If(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}function Tf(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}function Cf(t){return"x"===t.dim?0:1}function Df(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return p(GS,function(t){return t+"transition:"+i}).join(";")}function Af(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t.getFont()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),HS(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function kf(t){var e=[],i=t.get("transitionDuration"),n=t.get("backgroundColor"),r=t.getModel("textStyle"),a=t.get("padding");return i&&e.push(Df(i)),n&&(jf.canvasSupported?e.push("background-Color:"+n):(e.push("background-Color:#"+ti(n)),e.push("filter:alpha(opacity=70)"))),HS(["width","color","radius"],function(i){var n="border-"+i,r=WS(n),a=t.get(r);null!=a&&e.push(n+":"+a+("color"===i?"":"px"))}),e.push(Af(r)),null!=a&&e.push("padding:"+vy(a).join("px ")+"px"),e.join(";")+";"}function Pf(t,e){if(jf.wxa)return null;var i=document.createElement("div"),n=this._zr=e.getZr();this.el=i,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r._enterable){var i=n.handler;xe(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function Lf(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}function Of(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(uo.isInstance(i)&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new uo(i,e,e.ecModel))}return e}function Ef(t,e){return t.dispatchAction||y(e.dispatchAction,e)}function zf(t,e,i,n,r,a,o){var s=i.getOuterSize(),l=s.width,h=s.height;return null!=a&&(t+l+a>n?t-=l+a:t+=a),null!=o&&(e+h+o>r?e-=h+o:e+=o),[t,e]}function Bf(t,e,i,n,r){var a=i.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,n)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function Rf(t,e,i){var n=i[0],r=i[1],a=5,o=0,s=0,l=e.width,h=e.height;switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+h/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-n/2,s=e.y+h+a;break;case"left":o=e.x-n-a,s=e.y+h/2-r/2;break;case"right":o=e.x+l+a,s=e.y+h/2-r/2}return[o,s]}function Nf(t){return"center"===t||"middle"===t}function Ff(t){return ZS(t)}function Vf(){if(!QS&&JS){QS=!0;var t=JS.styleSheets;t.length<31?JS.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}function Hf(t){return parseInt(t,10)}function Wf(t,e){Vf(),this.root=t,this.storage=e;var i=document.createElement("div"),n=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var r=e.delFromStorage,a=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(n)},e.addToStorage=function(t){t.onAdd&&t.onAdd(n),a.call(e,t)},this._firstPaint=!0}function Gf(t){return function(){tg('In IE8.0 VML mode painter not support method "'+t+'"')}}var Xf=2311,Yf=function(){return Xf++},Uf={};Uf="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var jf=Uf,qf={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Zf={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Kf=Object.prototype.toString,$f=Array.prototype,Qf=$f.forEach,Jf=$f.filter,tp=$f.slice,ep=$f.map,ip=$f.reduce,np={},rp=function(){return np.createCanvas()};np.createCanvas=function(){return document.createElement("canvas")};var ap,op="__ec_primitive__";R.prototype={constructor:R,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var i in this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var sp=(Object.freeze||Object)({$override:i,clone:n,merge:r,mergeAll:a,extend:o,defaults:s,createCanvas:rp,getContext:l,indexOf:h,inherits:u,mixin:c,isArrayLike:d,each:f,map:p,reduce:g,filter:v,find:m,bind:y,curry:x,isArray:_,isFunction:w,isString:b,isObject:S,isBuiltInObject:M,isTypedArray:I,isDom:T,eqNaN:C,retrieve:D,retrieve2:A,retrieve3:k,slice:P,normalizeCssArray:L,assert:O,trim:E,setAsPrimitive:z,isPrimitive:B,createHashMap:N,concatArray:F,noop:V}),lp="undefined"==typeof Float32Array?Array:Float32Array,hp=q,up=Z,cp=ee,dp=ie,fp=(Object.freeze||Object)({create:H,copy:W,clone:G,set:X,add:Y,scaleAndAdd:U,sub:j,len:q,length:hp,lenSquare:Z,lengthSquare:up,mul:K,div:$,dot:Q,scale:J,normalize:te,distance:ee,dist:cp,distanceSquare:ie,distSquare:dp,negate:ne,lerp:re,applyTransform:ae,min:oe,max:se});le.prototype={constructor:le,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(he(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(he(e,t),"drag",t.event);var o=this.findHover(i,n,e).target,s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(he(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(he(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(he(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(he(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var pp=Array.prototype.slice,gp=function(t){this._$handlers={},this._$eventProcessor=t};gp.prototype={constructor:gp,one:function(t,e,i,n){return ce(this,t,e,i,n,!0)},on:function(t,e,i,n){return ce(this,t,e,i,n,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t].length;a>r;r++)i[t][r].h!==e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>3&&(n=pp.call(n,1));for(var a=e.length,o=0;a>o;){var s=e[o];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))o++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,n[1]);break;case 3:s.h.call(s.ctx,n[1],n[2]);break;default:s.h.apply(s.ctx,n)}s.one?(e.splice(o,1),a--):o++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>4&&(n=pp.call(n,1,n.length-1));for(var a=n[n.length-1],o=e.length,s=0;o>s;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(a);break;case 2:l.h.call(a,n[1]);break;case 3:l.h.call(a,n[1],n[2]);break;default:l.h.apply(a,n)}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var vp=Math.log(2),mp="undefined"!=typeof window&&!!window.addEventListener,yp=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,xp="___zrEVENTSAVED",_p=[],wp=mp?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},bp=function(){this._track=[]};bp.prototype={constructor:bp,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},a=0,o=n.length;o>a;a++){var s=n[a],l=pe(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in Sp)if(Sp.hasOwnProperty(e)){var i=Sp[e](this._track,t);if(i)return i}}};var Sp={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&r.length>1&&n&&n.length>1){var a=be(n)/be(r);!isFinite(a)&&(a=1),e.pinchScale=a;var o=Se(n);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},Mp="silent";Te.prototype.dispose=function(){};var Ip=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Tp=function(t,e,i,n){gp.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new Te,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,le.call(this),this.setHandlerProxy(i)};Tp.prototype={constructor:Tp,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(Ip,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,i=t.zrY,n=De(this,e,i),r=this._hovered,a=r.target;a&&!a.__zr&&(r=this.findHover(r.x,r.y),a=r.target);var o=this._hovered=n?{x:e,y:i}:this.findHover(e,i),s=o.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,i=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!i&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){t=t||{};var n=t.target;if(!n||!n.silent){for(var r="on"+e,a=Me(e,t,i);n&&(n[r]&&(a.cancelBubble=n[r].call(n,a)),n.trigger(e,a),n=n.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},a=n.length-1;a>=0;a--){var o;if(n[a]!==i&&!n[a].ignore&&(o=Ce(n[a],t,e))&&(!r.topTarget&&(r.topTarget=n[a]),o!==Mp)){r.target=n[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new bp);var i=this._gestureMgr;"start"===e&&i.clear();var n=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),n){var r=n.type;t.gestureEvent=r,this.dispatchToElement({target:n.target},r,n.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){Tp.prototype[t]=function(e){var i,n,r=e.zrX,a=e.zrY,o=De(this,r,a);if("mouseup"===t&&o||(i=this.findHover(r,a),n=i.target),"mousedown"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if("mouseup"===t)this._upEl=n;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||cp(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(i,t,e)}}),c(Tp,gp),c(Tp,le);var Cp="undefined"==typeof Float32Array?Array:Float32Array,Dp=(Object.freeze||Object)({create:Ae,identity:ke,copy:Pe,mul:Le,translate:Oe,rotate:Ee,scale:ze,invert:Be,clone:Re}),Ap=ke,kp=5e-5,Pp=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},Lp=Pp.prototype;Lp.transform=null,Lp.needLocalTransform=function(){return Ne(this.rotation)||Ne(this.position[0])||Ne(this.position[1])||Ne(this.scale[0]-1)||Ne(this.scale[1]-1)};var Op=[];Lp.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;if(!i&&!e)return void(n&&Ap(n));n=n||Ae(),i?this.getLocalTransform(n):Ap(n),e&&(i?Le(n,t.transform,n):Pe(n,t.transform)),this.transform=n;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(Op);var a=Op[0]<0?-1:1,o=Op[1]<0?-1:1,s=((Op[0]-a)*r+a)/Op[0]||0,l=((Op[1]-o)*r+o)/Op[1]||0;n[0]*=s,n[1]*=s,n[2]*=l,n[3]*=l}this.invTransform=this.invTransform||Ae(),Be(this.invTransform,n)},Lp.getLocalTransform=function(t){return Pp.getLocalTransform(this,t)},Lp.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},Lp.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var Ep=[],zp=Ae();Lp.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=this.position,r=this.scale;Ne(e-1)&&(e=Math.sqrt(e)),Ne(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),n[0]=t[4],n[1]=t[5],r[0]=e,r[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},Lp.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(Le(Ep,t.invTransform,e),e=Ep);var i=this.origin;i&&(i[0]||i[1])&&(zp[4]=i[0],zp[5]=i[1],Le(Ep,e,zp),Ep[4]-=i[0],Ep[5]-=i[1],e=Ep),this.setLocalTransform(e)}},Lp.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},Lp.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ae(i,i,n),i},Lp.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ae(i,i,n),i},Pp.getLocalTransform=function(t,e){e=e||[],Ap(e);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,a=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),ze(e,e,n),r&&Ee(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=a[0],e[5]+=a[1],e};var Bp={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)
},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-Bp.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*Bp.bounceIn(2*t):.5*Bp.bounceOut(2*t-1)+.5}};Fe.prototype={constructor:Fe,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var i=(t-this._startTime-this._pausedTime)/this._life;if(!(0>i)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?Bp[n]:n,a="function"==typeof r?r(i):i;return this.fire("frame",a),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var Rp=function(){this.head=null,this.tail=null,this._len=0},Np=Rp.prototype;Np.insert=function(t){var e=new Fp(t);return this.insertEntry(e),e},Np.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Np.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Np.len=function(){return this._len},Np.clear=function(){this.head=this.tail=null,this._len=0};var Fp=function(t){this.value=t,this.next,this.prev},Vp=function(t){this._list=new Rp,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},Hp=Vp.prototype;Hp.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var a=i.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new Fp(e),o.key=t,i.insertEntry(o),n[t]=o}return r},Hp.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value):void 0},Hp.clear=function(){this._list.clear(),this._map={}};var Wp={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},Gp=new Vp(20),Xp=null,Yp=ei,Up=ii,jp=(Object.freeze||Object)({parse:Ke,lift:Je,toHex:ti,fastLerp:ei,fastMapToColor:Yp,lerp:ii,mapToColor:Up,modifyHSL:ni,modifyAlpha:ri,stringify:ai}),qp=Array.prototype.slice,Zp=function(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||oi,this._setter=n||si,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};Zp.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:gi(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;e>i;i++)t[i].call(this)},start:function(t,e){var i,n=this,r=0,a=function(){r--,r||n._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=yi(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Kp=1;"undefined"!=typeof window&&(Kp=Math.max(window.devicePixelRatio||1,1));var $p=0,Qp=Kp,Jp=function(){};1===$p&&(Jp=console.error);var tg=Jp,eg=function(){this.animators=[]};eg.prototype={constructor:eg,animate:function(t,e){var i,n=!1,r=this,a=this.__zr;if(t){var o=t.split("."),s=r;n="shape"===o[0];for(var l=0,u=o.length;u>l;l++)s&&(s=s[o[l]]);s&&(i=s)}else i=r;if(!i)return void tg('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new Zp(i,e);return d.during(function(){r.dirty(n)}).done(function(){c.splice(h(c,d),1)}),c.push(d),a&&a.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;i>n;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,a){xi(this,t,e,i,n,r,a)},animateFrom:function(t,e,i,n,r,a){xi(this,t,e,i,n,r,a,!0)}};var ig=function(t){Pp.call(this,t),gp.call(this,t),eg.call(this,t),this.id=t.id||Yf()};ig.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(S(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(ig,eg),c(ig,Pp),c(ig,gp);var ng=ae,rg=Math.min,ag=Math.max;bi.prototype={constructor:bi,union:function(t){var e=rg(t.x,this.x),i=rg(t.y,this.y);this.width=ag(t.x+t.width,this.x+this.width)-e,this.height=ag(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this.height,ng(t,t,r),ng(e,e,r),ng(i,i,r),ng(n,n,r),this.x=rg(t[0],e[0],i[0],n[0]),this.y=rg(t[1],e[1],i[1],n[1]);var a=ag(t[0],e[0],i[0],n[0]),o=ag(t[1],e[1],i[1],n[1]);this.width=a-this.x,this.height=o-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=Ae();return Oe(r,r,[-e.x,-e.y]),ze(r,r,[i,n]),Oe(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof bi||(t=bi.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(o>n||i>s||l>a||r>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new bi(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},bi.create=function(t){return new bi(t.x,t.y,t.width,t.height)};var og=function(t){t=t||{},ig.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};og.prototype={constructor:og,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);n>=0&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof og&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=h(n,t);return 0>r?this:(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof og&&t.delChildrenFromStorage(i)),e&&e.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof og&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof og&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof og&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new bi(0,0,0,0),n=t||this._children,r=[],a=0;a<n.length;a++){var o=n[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),l=o.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),e=e||i.clone(),e.union(i)):(e=e||s.clone(),e.union(s))}}return e||i}},u(og,ig);var sg=32,lg=7,hg=function(){this._roots=[],this._displayList=[],this._displayListLen=0};hg.prototype={constructor:hg,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;r>n;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,jf.canvasSupported&&ki(i,Pi)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof og&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof og&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,n=t.length;n>e;e++)this.delRoot(t[e]);else{var r=h(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof og&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:Pi};var ug={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},cg=function(t,e,i){return ug.hasOwnProperty(e)?i*=t.dpr:i},dg={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},fg=9,pg=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],gg=function(t){this.extendFrom(t,!1)};gg.prototype={constructor:gg,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var n=this,r=i&&i.style,a=!r||t.__attrCachedBy!==dg.STYLE_BIND;t.__attrCachedBy=dg.STYLE_BIND;for(var o=0;o<pg.length;o++){var s=pg[o],l=s[0];(a||n[l]!==r[l])&&(t[l]=cg(t,l,n[l]||s[1]))}if((a||n.fill!==r.fill)&&(t.fillStyle=n.fill),(a||n.stroke!==r.stroke)&&(t.strokeStyle=n.stroke),(a||n.opacity!==r.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(a||n.blend!==r.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var h=n.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||e!==!0&&(e===!1?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n="radial"===e.type?Oi:Li,r=n(t,e,i),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var vg=gg.prototype,mg=0;mg<pg.length;mg++){var yg=pg[mg];yg[0]in vg||(vg[yg[0]]=yg[1])}gg.getGradient=vg.getGradient;var xg=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};xg.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var _g=function(t,e,i){var n;i=i||Qp,"string"==typeof t?n=zi(t,e,i):S(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var r=n.style;r&&(n.onselectstart=Ei,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i};_g.prototype={constructor:_g,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=zi("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,a&&(a.width=t*i,a.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i=this.dom,n=this.ctx,r=i.width,a=i.height,e=e||this.clearColor,o=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,r/l,a/l)),n.clearRect(0,0,r,a),e&&"transparent"!==e){var h;e.colorStops?(h=e.__canvasGradient||gg.getGradient(n,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=h):e.image&&(h=xg.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=h||e,n.fillRect(0,0,r,a),n.restore()}if(o){var u=this.domBack;n.save(),n.globalAlpha=s,n.drawImage(u,0,0,r,a),n.restore()}}};var wg="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},bg=new Vp(50),Sg={},Mg=0,Ig=5e3,Tg=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,Cg="12px sans-serif",Dg={};Dg.measureText=function(t,e){var i=l();return i.font=e||Cg,i.measureText(t)};var Ag=Cg,kg={left:1,right:1,center:1},Pg={top:1,bottom:1,middle:1},Lg=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],Og={},Eg={},zg=new bi,Bg=function(){};Bg.prototype={constructor:Bg,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&on(i,!0);var n=i.text;if(null!=n&&(n+=""),Sn(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(zg.copy(e),zg.applyTransform(r),e=zg),ln(this,t,n,i,e,fg),t.restore()}}},Mn.prototype={constructor:Mn,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?ig.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new gg(t,this),this.dirty(!1),this},calculateTextPosition:null},u(Mn,ig),c(Mn,Bg),In.prototype={constructor:In,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=Ri(n,this._image,this,this.onload);if(r&&Fi(r)){var a=i.x||0,o=i.y||0,s=i.width,l=i.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(r,u,c,i.sWidth,i.sHeight,a,o,s,l)}else if(i.sx&&i.sy){var u=i.sx,c=i.sy,d=s-u,f=l-c;t.drawImage(r,u,c,d,f,a,o,s,l)}else t.drawImage(r,a,o,s,l);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new bi(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},u(In,Mn);var Rg=1e5,Ng=314159,Fg=.01,Vg=.001,Hg=new bi(0,0,0,0),Wg=new bi(0,0,0,0),Gg=function(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=o({},i||{}),this.dpr=i.devicePixelRatio||Qp,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,n){var l=t.width,h=t.height;null!=i.width&&(l=i.width),null!=i.height&&(h=i.height),this.dpr=i.devicePixelRatio||1,t.width=l*this.dpr,t.height=h*this.dpr,this._width=l,this._height=h;var u=new _g(t,this,this.dpr);u.__builtin__=!0,u.initContext(),s[Ng]=u,u.zlevel=Ng,a.push(Ng),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Pn(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};Gg.prototype={constructor:Gg,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var n=0;n<i.length;n++){var r=i[n],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===n?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return i.__from=t,t.__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=h(i,e);n>=0&&i.splice(n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){ki(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(Rg));var n={};i.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,n))):(t.splice(r,1),o.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(Rg)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!n){var r=this;wg(function(){r._paintList(t,e,i)})}}},_compositeManually:function(){var t=this.getLayer(Ng).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},_doPaintList:function(t,e){for(var i=[],n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n],a=this._layers[r];a.__builtin__&&a!==this._hoverlayer&&(a.__dirty||e)&&i.push(a)}for(var o=!0,s=0;s<i.length;s++){var a=i[s],l=a.ctx,h={};l.save();var u=e?a.__startIndex:a.__drawIndex,c=!e&&a.incremental&&Date.now,d=c&&Date.now(),p=a.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(a.__startIndex===a.__endIndex)a.clear(!1,p);else if(u===a.__startIndex){var g=t[u];g.incremental&&g.notClear&&!e||a.clear(!1,p)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=a.__startIndex);for(var v=u;v<a.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,a,e,h),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}a.__drawIndex=v,a.__drawIndex<a.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return jf.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,i,n){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&Dn(t,this._width,this._height))){var o=t.__clipPaths,s=n.prevElClipPaths;(!s||An(o,s))&&(s&&(r.restore(),n.prevElClipPaths=null,n.prevEl=null),o&&(r.save(),kn(o,r),n.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Ng);var i=this._layers[t];return i||(i=new _g("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]&&r(i,this._layerConfig[t],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,a=null,o=-1,s=this._domRoot;if(i[t])return void tg("ZLevel "+t+" has been used already");if(!Cn(e))return void tg("Layer of zlevel "+t+" is not valid");if(r>0&&t>n[0]){for(o=0;r-1>o&&!(n[o]<t&&n[o+1]>t);o++);a=i[n[o]]}if(n.splice(o+1,0,t),i[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}for(var r=null,a=0,i=0;i<t.length;i++){var o,n=t[i],s=n.zlevel;n.incremental?(o=this.getLayer(s+Vg,this._needsManuallyCompositing),o.incremental=!0,a=1):o=this.getLayer(s+(a>0?Fg:0),this._needsManuallyCompositing),o.__builtin__||tg("ZLevel "+s+" has been used by unkown layer "+o.id),o!==r&&(o.__used=!0,o.__startIndex!==i&&(o.__dirty=!0),o.__startIndex=i,o.__drawIndex=o.incremental?-1:i,e(i),r=o),n.__dirty&&(o.__dirty=!0,o.incremental&&o.__drawIndex<0&&(o.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?r(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Fg){var o=this._layers[a];r(o,i[t],!0)}}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(h(i,t),1))},resize:function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(Ng).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Ng].dom;var e=new _g("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[n]||Tn(s[i])||Tn(o.style[i]))-(Tn(s[r])||0)-(Tn(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,h=a.hasStroke()?a.lineWidth:0,u=Math.max(h/2,-s+o),c=Math.max(h/2,s+o),d=Math.max(h/2,-l+o),f=Math.max(h/2,l+o),p=r.width+u+c,g=r.height+d+f;i.width=p*e,i.height=g*e,n.scale(e,e),n.clearRect(0,0,p,g),n.dpr=e;
var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var m=In,y=new m({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var Xg=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,gp.call(this)};Xg.prototype={constructor:Xg,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=h(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r.length;for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(wg(t),!e._paused&&e._update())}var e=this;this._running=!0,wg(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var i=new Zp(t,e.loop,e.getter,e.setter);return this.addAnimator(i),i}},c(Xg,gp);var Yg=300,Ug=jf.domSupported,jg=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],i={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=p(t,function(t){var e=t.replace("mouse","pointer");return i.hasOwnProperty(e)?e:t});return{mouse:t,touch:e,pointer:n}}(),qg={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Zg=Nn.prototype;Zg.stopPropagation=Zg.stopImmediatePropagation=Zg.preventDefault=V;var Kg={mousedown:function(t){t=xe(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=xe(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||Gn(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=xe(this.dom,t),Gn(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=xe(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=Rn(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=xe(this.dom,t),zn(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Kg.mousemove.call(this,t),Kg.mousedown.call(this,t)},touchmove:function(t){t=xe(this.dom,t),zn(t),this.handler.processGesture(t,"change"),Kg.mousemove.call(this,t)},touchend:function(t){t=xe(this.dom,t),zn(t),this.handler.processGesture(t,"end"),Kg.mouseup.call(this,t),+new Date-this._lastTouchMoment<Yg&&Kg.click.call(this,t)},pointerdown:function(t){Kg.mousedown.call(this,t)},pointermove:function(t){On(t)||Kg.mousemove.call(this,t)},pointerup:function(t){Kg.mouseup.call(this,t)},pointerout:function(t){On(t)||Kg.mouseout.call(this,t)}};f(["click","mousewheel","dblclick","contextmenu"],function(t){Kg[t]=function(e){e=xe(this.dom,e),this.trigger(t,e)}});var $g={pointermove:function(t){On(t)||$g.mousemove.call(this,t)},pointerup:function(t){$g.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;Gn(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}},Qg=Yn.prototype;Qg.dispose=function(){Wn(this._localHandlerScope),Ug&&Wn(this._globalHandlerScope)},Qg.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(Yn,gp);var Jg=!jf.canvasSupported,tv={canvas:Gg},ev={},iv="4.2.0",nv=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new hg,a=i.renderer;if(Jg){if(!tv.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&tv[a]||(a="canvas");var o=new tv[a](e,r,i,t);this.storage=r,this.painter=o;var s=jf.node||jf.worker?null:new Yn(o.getViewportRoot(),o.root);this.handler=new Tp(r,o,s,o.root),this.animation=new Xg({stage:{update:y(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(n)}};nv.prototype={constructor:nv,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,Kn(this.id)}};var rv=(Object.freeze||Object)({version:iv,init:Un,dispose:jn,getInstance:qn,registerPainter:Zn}),av=f,ov=S,sv=_,lv="series\x00",hv=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],uv=0,cv=".",dv="___EC__COMPONENT__CONTAINER___",fv=0,pv=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,i,n){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(i&&h(i,o)>=0||n&&h(n,o)<0)){var s=e.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}},gv=pv([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),vv={getLineStyle:function(t){var e=gv(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?!1:"dashed"===e?[n,n]:[i,i]}},mv=pv([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),yv={getAreaStyle:function(t,e){return mv(this,t,e)}},xv=Math.pow,_v=Math.sqrt,wv=1e-8,bv=1e-4,Sv=_v(3),Mv=1/3,Iv=H(),Tv=H(),Cv=H(),Dv=Math.min,Av=Math.max,kv=Math.sin,Pv=Math.cos,Lv=2*Math.PI,Ov=H(),Ev=H(),zv=H(),Bv=[],Rv=[],Nv={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Fv=[],Vv=[],Hv=[],Wv=[],Gv=Math.min,Xv=Math.max,Yv=Math.cos,Uv=Math.sin,jv=Math.sqrt,qv=Math.abs,Zv="undefined"!=typeof Float32Array,Kv=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Kv.prototype={constructor:Kv,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,i){i=i||0,this._ux=qv(i/Qp/t)||0,this._uy=qv(i/Qp/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Nv.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=qv(t-this._xi)>this._ux||qv(e-this._yi)>this._uy||this._len<5;return this.addData(Nv.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(Nv.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(Nv.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(Nv.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=Yv(r)*i+t,this._yi=Uv(r)*i+e,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Nv.R,t,e,i,n),this},closePath:function(){this.addData(Nv.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Zv||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();Zv&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[n++]=a[o];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=jv(u*u+c*c),f=l,p=h,g=o.length;for(u/=d,c/=d,0>a&&(a=r+a),a%=r,f-=a*u,p-=a*c;u>0&&t>=f||0>u&&f>=t||0===u&&(c>0&&e>=p||0>c&&p>=e);)n=this._dashIdx,i=o[n],f+=u*i,p+=c*i,this._dashIdx=(n+1)%g,u>0&&l>f||0>u&&f>l||c>0&&h>p||0>c&&p>h||s[n%2?"moveTo":"lineTo"](u>=0?Gv(f,t):Xv(f,t),c>=0?Gv(p,e):Xv(p,e));u=f-t,c=p-e,this._dashOffset=-jv(u*u+c*c)},_dashedBezierTo:function(t,e,i,n,r,a){var o,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=wr,y=0,x=this._dashIdx,_=f.length,w=0;for(0>d&&(d=c+d),d%=c,o=0;1>o;o+=.1)s=m(g,t,i,r,o+.1)-m(g,t,i,r,o),l=m(v,e,n,a,o+.1)-m(v,e,n,a,o),y+=jv(s*s+l*l);for(;_>x&&(w+=f[x],!(w>d));x++);for(o=(w-d)/y;1>=o;)h=m(g,t,i,r,o),u=m(v,e,n,a,o),x%2?p.moveTo(h,u):p.lineTo(h,u),o+=f[x]/y,x=(x+1)%_;x%2!==0&&p.lineTo(r,a),s=r-h,l=a-u,this._dashOffset=-jv(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Zv&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Fv[0]=Fv[1]=Hv[0]=Hv[1]=Number.MAX_VALUE,Vv[0]=Vv[1]=Wv[0]=Wv[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,r=0,a=0;a<t.length;){var o=t[a++];switch(1===a&&(e=t[a],i=t[a+1],n=e,r=i),o){case Nv.M:n=t[a++],r=t[a++],e=n,i=r,Hv[0]=n,Hv[1]=r,Wv[0]=n,Wv[1]=r;break;case Nv.L:Er(e,i,t[a],t[a+1],Hv,Wv),e=t[a++],i=t[a++];break;case Nv.C:zr(e,i,t[a++],t[a++],t[a++],t[a++],t[a],t[a+1],Hv,Wv),e=t[a++],i=t[a++];break;case Nv.Q:Br(e,i,t[a++],t[a++],t[a],t[a+1],Hv,Wv),e=t[a++],i=t[a++];break;case Nv.A:var s=t[a++],l=t[a++],h=t[a++],u=t[a++],c=t[a++],d=t[a++]+c;a+=1;var f=1-t[a++];1===a&&(n=Yv(c)*h+s,r=Uv(c)*u+l),Rr(s,l,h,u,c,d,f,Hv,Wv),e=Yv(d)*h+s,i=Uv(d)*u+l;break;case Nv.R:n=e=t[a++],r=i=t[a++];var p=t[a++],g=t[a++];Er(n,r,n+p,r+g,Hv,Wv);break;case Nv.Z:e=n,i=r}oe(Fv,Fv,Hv),se(Vv,Vv,Wv)}return 0===a&&(Fv[0]=Fv[1]=Vv[0]=Vv[1]=0),new bi(Fv[0],Fv[1],Vv[0]-Fv[0],Vv[1]-Fv[1])},rebuildPath:function(t){for(var e,i,n,r,a,o,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;u>c;){var d=s[c++];switch(1===c&&(n=s[c],r=s[c+1],e=n,i=r),d){case Nv.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case Nv.L:a=s[c++],o=s[c++],(qv(a-n)>l||qv(o-r)>h||c===u-1)&&(t.lineTo(a,o),n=a,r=o);break;case Nv.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Nv.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Nv.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],x=s[c++],_=s[c++],w=g>v?g:v,b=g>v?1:g/v,S=g>v?v/g:1,M=Math.abs(g-v)>.001,I=m+y;M?(t.translate(f,p),t.rotate(x),t.scale(b,S),t.arc(0,0,w,m,I,1-_),t.scale(1/b,1/S),t.rotate(-x),t.translate(-f,-p)):t.arc(f,p,w,m,I,1-_),1===c&&(e=Yv(m)*g+f,i=Uv(m)*v+p),n=Yv(I)*g+f,r=Uv(I)*v+p;break;case Nv.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Nv.Z:t.closePath(),n=e,r=i}}}},Kv.CMD=Nv;var $v=2*Math.PI,Qv=2*Math.PI,Jv=Kv.CMD,tm=2*Math.PI,em=1e-4,im=[-1,-1,-1],nm=[-1,-1],rm=xg.prototype.getCanvasPattern,am=Math.abs,om=new Kv(!0);Qr.prototype={constructor:Qr,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var i=this.style,n=this.path||om,r=i.hasStroke(),a=i.hasFill(),o=i.fill,s=i.stroke,l=a&&!!o.colorStops,h=r&&!!s.colorStops,u=a&&!!o.image,c=r&&!!s.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=i.getGradient(t,o,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=rm.call(o,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=rm.call(s,t));var f=i.lineDash,p=i.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(n.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&r?(n.beginPath(t),f&&!g&&(n.setLineDash(f),n.setLineDashOffset(p)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=i.fillOpacity){var m=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,n.fill(t),t.globalAlpha=m}else n.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=i.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,n.stroke(t),t.globalAlpha=m}else n.stroke(t);f&&g&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Kv},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n||(n=this.path=new Kv),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r.height+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),$r(a,o/s,t,e)))return!0}if(r.hasFill())return Kr(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Mn.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(S(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&am(t[0]-1)>1e-10&&am(t[3]-1)>1e-10?Math.sqrt(am(t[0]*t[3]-t[2]*t[1])):1}},Qr.extend=function(t){var e=function(e){Qr.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var i=t.shape;if(i){this.shape=this.shape||{};var n=this.shape;for(var r in i)!n.hasOwnProperty(r)&&i.hasOwnProperty(r)&&(n[r]=i[r])}t.init&&t.init.call(this,e)};u(e,Qr);for(var i in t)"style"!==i&&"shape"!==i&&(e.prototype[i]=t[i]);return e},u(Qr,Mn);var sm=Kv.CMD,lm=[[],[],[]],hm=Math.sqrt,um=Math.atan2,cm=function(t,e){var i,n,r,a,o,s,l=t.data,h=sm.M,u=sm.C,c=sm.L,d=sm.R,f=sm.A,p=sm.Q;for(r=0,a=0;r<l.length;){switch(i=l[r++],a=r,n=0,i){case h:n=1;break;case c:n=1;break;case u:n=3;break;case p:n=2;break;case f:var g=e[4],v=e[5],m=hm(e[0]*e[0]+e[1]*e[1]),y=hm(e[2]*e[2]+e[3]*e[3]),x=um(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=x,l[r++]+=x,r+=2,a=r;break;case d:s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1],s[0]+=l[r++],s[1]+=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}for(o=0;n>o;o++){var s=lm[o];s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}}},dm=Math.sqrt,fm=Math.sin,pm=Math.cos,gm=Math.PI,vm=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},mm=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(vm(t)*vm(e))},ym=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(mm(t,e))},xm=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,_m=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,wm=function(t){Mn.call(this,t)};wm.prototype={constructor:wm,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&on(i,!0),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;return null!=n&&(n+=""),Sn(n,i)?(this.setTransform(t),ln(this,t,n,i,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=dg.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&on(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var i=Wi(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,xn(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},u(wm,Mn);var bm=Qr.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Sm=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],Mm=function(t){return jf.browser.ie&&jf.browser.version>=11?function(){var e,i=this.__clipPaths,n=this.style;if(i)for(var r=0;r<i.length;r++){var a=i[r],o=a&&a.shape,s=a&&a.type;if(o&&("sector"===s&&o.startAngle===o.endAngle||"rect"===s&&(!o.width||!o.height))){for(var l=0;l<Sm.length;l++)Sm[l][2]=n[Sm[l][0]],n[Sm[l][0]]=Sm[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<Sm.length;l++)n[Sm[l][0]]=Sm[l][2]}:t},Im=Qr.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:Mm(Qr.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*r+i,u*r+n),t.lineTo(h*a+i,u*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}}),Tm=Qr.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}}),Cm=function(t,e){for(var i=t.length,n=[],r=0,a=1;i>a;a++)r+=ee(t[a-1],t[a]);var o=r/2;o=i>o?i:o;for(var a=0;o>a;a++){var s,l,h,u=a/(o-1)*(e?i:i-1),c=Math.floor(u),d=u-c,f=t[c%i];e?(s=t[(c-1+i)%i],l=t[(c+1)%i],h=t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[c>i-2?i-1:c+1],h=t[c>i-3?i-1:c+2]);var p=d*d,g=d*p;n.push([aa(s[0],f[0],l[0],h[0],d,p,g),aa(s[1],f[1],l[1],h[1],d,p,g)])}return n},Dm=function(t,e,i,n){var r,a,o,s,l=[],h=[],u=[],c=[];if(n){o=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)oe(o,o,t[d]),se(s,s,t[d]);oe(o,o,n[0]),se(s,s,n[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(i)r=t[d?d-1:f-1],a=t[(d+1)%f];else{if(0===d||d===f-1){l.push(G(t[d]));continue}r=t[d-1],a=t[d+1]}j(h,a,r),J(h,h,e);var g=ee(p,r),v=ee(p,a),m=g+v;0!==m&&(g/=m,v/=m),J(u,h,-g),J(c,h,v);var y=Y([],p,u),x=Y([],p,c);n&&(se(y,y,o),oe(y,y,s),se(x,x,o),oe(x,x,s)),l.push(y),l.push(x)}return i&&l.push(l.shift()),l},Am=Qr.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){oa(t,e,!0)}}),km=Qr.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){oa(t,e,!1)}}),Pm=Math.round,Lm={},Om=Qr.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(la(Lm,e,this.style),i=Lm.x,n=Lm.y,r=Lm.width,a=Lm.height,Lm.r=e.r,e=Lm):(i=e.x,n=e.y,r=e.width,a=e.height),e.r?an(t,e):t.rect(i,n,r,a),t.closePath()}}),Em={},zm=Qr.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(sa(Em,e,this.style),i=Em.x1,n=Em.y1,r=Em.x2,a=Em.y2):(i=e.x1,n=e.y1,r=e.x2,a=e.y2);var o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),Bm=[],Rm=Qr.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,n),null==l||null==h?(1>u&&(Pr(i,o,r,u,Bm),o=Bm[1],r=Bm[2],Pr(n,s,a,u,Bm),s=Bm[1],a=Bm[2]),t.quadraticCurveTo(o,s,r,a)):(1>u&&(Ir(i,o,l,r,u,Bm),o=Bm[1],l=Bm[2],r=Bm[3],Ir(n,s,h,a,u,Bm),s=Bm[1],h=Bm[2],a=Bm[3]),t.bezierCurveTo(o,s,l,h,r,a)))},pointAt:function(t){return ua(this.shape,t,!1)},tangentAt:function(t){var e=ua(this.shape,t,!0);return te(e,e)}}),Nm=Qr.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*r+i,h*r+n),t.arc(i,n,r,a,o,!s)}}),Fm=Qr.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1],t[i].segmentIgnoreThreshold)},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),Qr.prototype.getBoundingRect.call(this)}}),Vm=function(t){this.colorStops=t||[]};Vm.prototype={constructor:Vm,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var Hm=function(t,e,i,n,r,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=a||!1,Vm.call(this,r)};Hm.prototype={constructor:Hm},u(Hm,Vm);var Wm=function(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,Vm.call(this,n)};Wm.prototype={constructor:Wm},u(Wm,Vm),ca.prototype.incremental=!0,ca.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},ca.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},ca.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},ca.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},ca.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},ca.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,e===this._cursor?null:this._displayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===e?null:this._temporaryDisplayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var Gm=[];ca.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new bi(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],n=i.getBoundingRect().clone();i.needLocalTransform()&&n.applyTransform(i.getLocalTransform(Gm)),t.union(n)}this._rect=t}return this._rect},ca.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();if(n.contain(i[0],i[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},u(ca,Mn);var Xm=Math.max,Ym=Math.min,Um={},jm=1,qm={color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},Zm="emphasis",Km="normal",$m=1,Qm={},Jm={},ty=ra,ey=ha,iy=N(),ny=0;pa("circle",bm),pa("sector",Im),pa("ring",Tm),pa("polygon",Am),pa("polyline",km),pa("rect",Om),pa("line",zm),pa("bezierCurve",Rm),pa("arc",Nm);var ry=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:jm,CACHED_LABEL_STYLE_PROPERTIES:qm,extendShape:da,extendPath:fa,registerShape:pa,getShapeClass:ga,makePath:va,makeImage:ma,mergePath:ty,resizePath:xa,subPixelOptimizeLine:_a,subPixelOptimizeRect:wa,subPixelOptimize:ey,setElementHoverStyle:Aa,setHoverStyle:za,setAsHighDownDispatcher:Ba,isHighDownDispatcher:Ra,getHighlightDigit:Na,setLabelStyle:Fa,modifyLabelStyle:Va,setTextStyle:Ha,setText:Wa,getFont:Za,updateProps:$a,initProps:Qa,getTransform:Ja,applyTransform:to,transformDirection:eo,groupTransition:io,clipPointsByRect:no,clipRectByRect:ro,createIcon:ao,linePolygonIntersect:oo,lineLineIntersect:so,Group:og,Image:In,Text:wm,Circle:bm,Sector:Im,Ring:Tm,Polygon:Am,Polyline:km,Rect:Om,Line:zm,BezierCurve:Rm,Arc:Nm,IncrementalDisplayable:ca,CompoundPath:Fm,LinearGradient:Hm,RadialGradient:Wm,BoundingRect:bi}),ay=["textStyle","color"],oy={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(ay):null)},getFont:function(){return Za({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return Wi(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},sy=pv([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),ly={getItemStyle:function(t,e){var i=sy(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},hy=c,uy=or();uo.prototype={constructor:uo,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:co(this.option,this.parsePath(t),!e&&fo(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&fo(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i,n=null==t?this.option:co(this.option,t=this.parsePath(t));return e=e||(i=fo(this,t))&&i.getModel(t),new uo(n,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(n(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){uy(this).getParent=t},isAnimationEnabled:function(){if(!jf.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},pr(uo),gr(uo),hy(uo,vv),hy(uo,yv),hy(uo,oy),hy(uo,ly);var cy=0,dy=1e-4,fy=9007199254740991,py=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,gy=(Object.freeze||Object)({linearMap:yo,parsePercent:xo,round:_o,asc:wo,getPrecision:bo,getPrecisionSafe:So,getPixelPrecision:Mo,getPercentWithPrecision:Io,MAX_SAFE_INTEGER:fy,remRadian:To,isRadianAroundZero:Co,parseDate:Do,quantity:Ao,quantityExponent:ko,nice:Po,quantile:Lo,reformIntervals:Oo,isNumeric:Eo}),vy=L,my=/([&<>"'])/g,yy={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},xy=["a","b","c","d","e","f","g"],_y=function(t,e){return"{"+t+(null==e?"":e)+"}"},wy=qi,by=(Object.freeze||Object)({addCommas:zo,toCamelCase:Bo,normalizeCssArray:vy,encodeHTML:Ro,formatTpl:No,formatTplSimple:Fo,getTooltipMarker:Vo,formatTime:Wo,capitalFirst:Go,truncateText:wy,getTextBoundingRect:Xo,getTextRect:Yo}),Sy=f,My=["left","right","top","bottom","width","height"],Iy=[["width","left","right"],["height","top","bottom"]],Ty=Uo,Cy=(x(Uo,"vertical"),x(Uo,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),Dy=or(),Ay=uo.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){uo.call(this,t,e,i,n),this.uid=po("ec_cpt_model")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Zo(t):{},a=e.getTheme();r(t,a.get(this.mainType)),r(t,this.getDefaultOption()),i&&qo(t,n,i)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;
e&&qo(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=Dy(this);if(!t.defaultOption){for(var e=[],i=this.constructor;i;){var n=i.prototype.defaultOption;n&&e.push(n),i=i.superClass}for(var a={},o=e.length-1;o>=0;o--)a=r(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});yr(Ay,{registerWhenExtend:!0}),go(Ay),vo(Ay,$o),c(Ay,Cy);var ky="";"undefined"!=typeof navigator&&(ky=navigator.platform||"");var Py={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:ky.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Ly=or(),Oy={clearColorPalette:function(){Ly(this).colorIdx=0,Ly(this).colorNameMap={}},getColorFromPalette:function(t,e,i){e=e||this;var n=Ly(e),r=n.colorIdx||0,a=n.colorNameMap=n.colorNameMap||{};if(a.hasOwnProperty(t))return a[t];var o=$n(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=i&&s?Qo(s,i):o;if(l=l||o,l&&l.length){var h=l[r];return t&&(a[t]=h),n.colorIdx=(r+1)%l.length,h}}},Ey="original",zy="arrayRows",By="objectRows",Ry="keyedColumns",Ny="unknown",Fy="typedArray",Vy="column",Hy="row";Jo.seriesDataToSource=function(t){return new Jo({data:t,sourceFormat:I(t)?Fy:Ey,fromDataset:!1})},gr(Jo);var Wy={Must:1,Might:2,Not:3},Gy=or(),Xy="\x00_ec_inner",Yy=uo.extend({init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new uo(i),this._optionManager=n},setOption:function(t,e){O(!(Xy in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):gs.call(this,n),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,n){var r=$n(t[e]),s=er(a.get(e),r);ir(s),f(s,function(t){var i=t.option;S(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=ms(e,i,t.exist))});var l=vs(a,n);i[e]=[],a.set(e,[]),f(s,function(t,n){var r=t.exist,s=t.option;if(O(S(s)||r,"Empty component definition"),s){var h=Ay.getClass(e,t.keyInfo.subType,!0);if(r&&r.constructor===h)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:n},t.keyInfo);r=new h(s,this,this,u),o(r,u),r.init(s,this,this,u),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);a.get(e)[n]=r,i[e][n]=r.option},this),"series"===e&&ys(this,a.get("series"))}var i=this.option,a=this._componentsMap,s=[];is(this),f(t,function(t,e){null!=t&&(Ay.hasClass(e)?e&&s.push(e):i[e]=null==i[e]?n(t):r(i[e],t,!0))}),Ay.topologicalTravel(s,Ay.getAllClassMainTypes(),e,this),this._seriesIndicesMap=N(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=n(this.option);return f(t,function(e,i){if(Ay.hasClass(i)){for(var e=$n(e),n=e.length-1;n>=0;n--)rr(e[n])&&e.splice(n,1);t[i]=e}}),delete t[Xy],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=i)_(i)||(i=[i]),o=v(p(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var s=_(n);o=v(a,function(t){return s&&h(n,t.id)>=0||!s&&t.id===n})}else if(null!=r){var l=_(r);o=v(a,function(t){return l&&h(r,t.name)>=0||!l&&t.name===r})}else o=a.slice();return xs(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return!t||null==t[e]&&null==t[i]&&null==t[n]?null:{mainType:r,index:t[e],id:t[i],name:t[n]}}function i(e){return t.filter?v(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),o=a?this.queryComponents(a):this._componentsMap.get(r);return i(xs(o,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if("function"==typeof t)i=e,e=t,n.each(function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(b(t))f(n.get(t),e,i);else if(S(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){f(this._seriesIndices,function(i){var n=this._componentsMap.get("series")[i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,i){f(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var i=v(this._componentsMap.get("series"),t,e);ys(this,i)},restoreData:function(t){var e=this._componentsMap;ys(this,e.get("series"));var i=[];e.each(function(t,e){i.push(e)}),Ay.topologicalTravel(i,Ay.getAllClassMainTypes(),function(i){f(e.get(i),function(e){("series"!==i||!fs(e,t))&&e.restoreData()})})}});c(Yy,Oy);var Uy=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],jy={};ws.prototype={constructor:ws,create:function(t,e){var i=[];f(jy,function(n){var r=n.create(t,e);i=i.concat(r||[])}),this._coordinateSystems=i},update:function(t,e){f(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},ws.register=function(t,e){jy[t]=e},ws.get=function(t){return jy[t]};var qy=f,Zy=n,Ky=p,$y=r,Qy=/^(min|max)?(.+)$/;bs.prototype={constructor:bs,setOption:function(t,e){t&&f($n(t.series),function(t){t&&t.data&&I(t.data)&&z(t.data)}),t=Zy(t);var i=this._optionBackup,n=Ss.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(Cs(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Ky(e.timelineOptions,Zy),this._mediaList=Ky(e.mediaList,Zy),this._mediaDefault=Zy(e.mediaDefault),this._currentMediaIndices=[],Zy(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=Zy(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),i=this._mediaList,n=this._mediaDefault,r=[],a=[];if(!i.length&&!n)return a;for(var o=0,s=i.length;s>o;o++)Ms(i[o].query,t,e)&&r.push(o);return!r.length&&n&&(r=[-1]),r.length&&!Ts(r,this._currentMediaIndices)&&(a=Ky(r,function(t){return Zy(-1===t?n.option:i[t].option)})),this._currentMediaIndices=r,a}};var Jy=f,tx=S,ex=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],ix=function(t,e){Jy(Es(t.series),function(t){tx(t)&&Os(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),Jy(i,function(e){Jy(Es(t[e]),function(t){t&&(Ps(t,"axisLabel"),Ps(t.axisPointer,"label"))})}),Jy(Es(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Ps(e,"axisLabel"),Ps(e&&e.axisPointer,"label")}),Jy(Es(t.calendar),function(t){As(t,"itemStyle"),Ps(t,"dayLabel"),Ps(t,"monthLabel"),Ps(t,"yearLabel")}),Jy(Es(t.radar),function(t){Ps(t,"name")}),Jy(Es(t.geo),function(t){tx(t)&&(Ls(t),Jy(Es(t.regions),function(t){Ls(t)}))}),Jy(Es(t.timeline),function(t){Ls(t),As(t,"label"),As(t,"itemStyle"),As(t,"controlStyle",!0);var e=t.data;_(e)&&f(e,function(t){S(t)&&(As(t,"label"),As(t,"itemStyle"))})}),Jy(Es(t.toolbox),function(t){As(t,"iconStyle"),Jy(t.feature,function(t){As(t,"iconStyle")})}),Ps(zs(t.axisPointer),"label"),Ps(zs(t.tooltip).axisPointer,"label")},nx=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],rx=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],ax=function(t,e){ix(t,e),t.series=$n(t.series),f(t.series,function(t){if(S(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var i=Bs(t,"pointer.color");null!=i&&Rs(t,"itemStyle.color",i)}Ns(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(rx,function(e){var i=t[e];i&&(_(i)||(i=[i]),f(i,function(t){Ns(t)}))})},ox=function(t){var e=N();t.eachSeries(function(t){var i=t.get("stack");if(i){var n=e.get(i)||e.set(i,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;n.length&&r.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(a)}}),e.each(Fs)},sx=Vs.prototype;sx.pure=!1,sx.persistent=!0,sx.getSource=function(){return this._source};var lx={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Gs},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],i=this._data,n=0;n<i.length;n++){var r=i[n];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Hs,getItem:Ws,appendData:Gs},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],i=this._source.dimensionsDefine,n=0;n<i.length;n++){var r=this._data[i[n].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,i){for(var n=e[i]||(e[i]=[]),r=0;r<(t||[]).length;r++)n.push(t[r])})}},original:{count:Hs,getItem:Ws,appendData:Gs},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var i=this._dimSize*t,n=0;n<this._dimSize;n++)e[n]=this._data[i+n];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},hx={arrayRows:Xs,objectRows:function(t,e,i,n){return null!=i?t[n]:t},keyedColumns:Xs,original:function(t,e,i){var n=Jn(t);return null!=i&&n instanceof Array?n[i]:n},typedArray:Xs},ux={arrayRows:Ys,objectRows:function(t,e){return Us(t[e],this._dimensionInfos[e])},keyedColumns:Ys,original:function(t,e,i,n){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&tr(t)&&(this.hasItemOption=!0),Us(r instanceof Array?r[n]:r,this._dimensionInfos[e])},typedArray:function(t,e,i,n){return t[n]}},cx=/\{@(.+?)\}/g,dx={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),a=i.getName(t),o=i.getRawDataItem(t),s=i.getItemVisual(t,"color"),l=i.getItemVisual(t,"borderColor"),h=this.ecModel.getComponent("tooltip"),u=h&&h.get("renderMode"),c=cr(u),d=this.mainType,f="series"===d,p=i.userOutput;return{componentType:d,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:f?this.subType:null,seriesIndex:this.seriesIndex,seriesId:f?this.id:null,seriesName:f?this.name:null,name:a,dataIndex:r,data:o,dataType:e,value:n,color:s,borderColor:l,dimensionNames:p?p.dimensionNames:null,encode:p?p.encode:null,marker:Vo({color:s,renderMode:c}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,i,n,r){e=e||"normal";var a=this.getData(i),o=a.getItemModel(t),s=this.getDataParams(t,i);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=o.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,s.dimensionIndex=n,l(s);if("string"==typeof l){var h=No(l,s);return h.replace(cx,function(e,i){var n=i.length;return"["===i.charAt(0)&&"]"===i.charAt(n-1)&&(i=+i.slice(1,n-1)),js(a,t,i)})}},getRawValue:function(t,e){return js(this.getData(e),t)},formatTooltip:function(){}},fx=Ks.prototype;fx.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var i=this._upstream,n=t&&t.skip;if(this._dirty&&i){var r=this.context;r.data=r.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),h=t&&t.modDataCount||0;(o!==l||s!==h)&&(a="reset");var u;(this._dirty||"reset"===a)&&(this._dirty=!1,u=Qs(this,n)),this._modBy=l,this._modDataCount=h;var c=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(u||f>d)){var p=this._progress;if(_(p))for(var g=0;g<p.length;g++)$s(this,p[g],d,f,l,h);else $s(this,p,d,f,l,h)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var px=function(){function t(){return i>n?n++:null}function e(){var t=n%o*r+Math.ceil(n/o),e=n>=i?null:a>t?t:n;return n++,e}var i,n,r,a,o,s={reset:function(l,h,u,c){n=l,i=h,r=u,a=c,o=Math.ceil(a/r),s.next=r>1&&a>0?e:t}};return s}();fx.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},fx.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},fx.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},fx.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},fx.getUpstream=function(){return this._upstream},fx.getDownstream=function(){return this._downstream},fx.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var gx=or(),vx=Ay.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.dataTask=Zs({count:el,reset:il}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,i),ns(this);var n=this.getInitialData(t,i);rl(n,this),this.dataTask.context.data=n,gx(this).dataBeforeProcessed=n,Js(this)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Zo(t):{},a=this.subType;Ay.hasClass(a)&&(a+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),Qn(t,"label",["show"]),this.fillDataTextStyle(t.data),i&&qo(t,n,i)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&qo(this.option,t,i),ns(this);var n=this.getInitialData(t,e);rl(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,gx(this).dataBeforeProcessed=n,Js(this)},fillDataTextStyle:function(t){if(t&&!I(t))for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&Qn(t[i],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=ol(this);if(e){var i=e.context.data;return null==t?i:i.getLinkedData(t)}return gx(this).data},setData:function(t){var e=ol(this);if(e){var i=e.context;i.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),i.outputData=t,e!==this.dataTask&&(i.data=t)}gx(this).data=t},getSource:function(){return es(this)},getRawData:function(){return gx(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,i,n){function r(i){function r(t,i){var r=c.getDimensionInfo(i);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+o.seriesIndex+"at"+u,p=Vo({color:y,type:"subItem",renderMode:n,markerId:f}),g="string"==typeof p?p:p.content,v=(a?g+Ro(r.displayName||"-")+": ":"")+Ro("ordinal"===d?t+"":"time"===d?e?"":Wo("yyyy/MM/dd hh:mm:ss",t):zo(t));v&&s.push(v),l&&(h[f]=y,++u)}}var a=g(i,function(t,e,i){var n=c.getDimensionInfo(i);return t|=n&&n.tooltip!==!1&&null!=n.displayName},0),s=[];d.length?f(d,function(e){r(js(c,t,e),e)}):f(i,r);var p=a?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:n,content:v,style:h}}function a(t){return{renderMode:n,content:Ro(zo(t)),style:h}}var o=this;n=n||"html";var s="html"===n?"<br/>":"\n",l="richText"===n,h={},u=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=_(v),y=c.getItemVisual(t,"color");S(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var x=p>1||m&&!p?r(v):a(p?js(c,t,d[0]):m?v[0]:v),w=x.content,b=o.seriesIndex+"at"+u,M=Vo({color:y,type:"item",renderMode:n,markerId:b});h[b]=y,++u;var I=c.getName(t),T=this.name;nr(this)||(T=""),T=T?Ro(T)+(e?": ":s):"";var C="string"==typeof M?M:M.content,D=e?C+T+w:T+C+(I?Ro(I)+": "+w:w);return{html:D,markers:h}},isAnimationEnabled:function(){if(jf.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,i){var n=this.ecModel,r=Oy.getColorFromPalette.call(this,t,e,i);return r||(r=n.getColorFromPalette(t,e,i)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(vx,dx),c(vx,Oy);var mx=function(){this.group=new og,this.uid=po("viewComponent")};mx.prototype={constructor:mx,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var yx=mx.prototype;yx.updateView=yx.updateLayout=yx.updateVisual=function(){},pr(mx),yr(mx,{registerWhenExtend:!0});var xx=function(){var t=or();return function(e){var i=t(e),n=e.pipelineContext,r=i.large,a=i.progressiveRender,o=i.large=n.large,s=i.progressiveRender=n.progressiveRender;return!!(r^o||a^s)&&"reset"}},_x=or(),bx=xx();sl.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,n){hl(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){hl(t.getData(),n,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var Sx=sl.prototype;Sx.updateView=Sx.updateLayout=Sx.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},pr(sl,["dispose"]),yr(sl,{registerWhenExtend:!0}),sl.markUpdateMethod=function(t,e){_x(t).updateMethod=e};var Mx={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},Ix="\x00__throttleOriginMethod",Tx="\x00__throttleRate",Cx="\x00__throttleType",Dx={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i=t.getData(),n=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(n),a=!w(r)||r instanceof Vm?null:r;(!r||a)&&(r=t.getColorFromPalette(t.name,null,e.getSeriesCount())),i.setVisual("color",r);var o=(t.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=t.get(o);if(i.setVisual("borderColor",s),!e.isSeriesFiltered(t)){a&&i.each(function(e){i.setItemVisual(e,"color",a(t.getDataParams(e)))});var l=function(t,e){var i=t.getItemModel(e),r=i.get(n,!0),a=i.get(o,!0);null!=r&&t.setItemVisual(e,"color",r),null!=a&&t.setItemVisual(e,"borderColor",a)};return{dataEach:i.hasItemOption?l:null}}}},Ax={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},kx=function(t,e){function i(t,e){if("string"!=typeof t)return t;var i=t;return f(e,function(t,e){i=i.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),i}function n(t){var e=o.get(t);if(null==e){for(var i=t.split("."),n=Ax.aria,r=0;r<i.length;++r)n=n[i[r]];return n}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function a(t){return Ax.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show")){if(o.get("description"))return void t.setAttribute("aria-label",o.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,h=o.get("data.maxCount")||10,u=o.get("series.maxCount")||10,c=Math.min(s,u);if(!(1>s)){var d=r();l=d?i(n("general.withTitle"),{title:d}):n("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=i(n(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,o=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=n(o?l+"withName":l+"withoutName"),r=i(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:a(t.subType)});var u=t.getData();window.data=u,r+=u.count()>h?i(n("data.partialData"),{displayCnt:h}):n("data.allData");for(var d=[],f=0;f<u.count();f++)if(h>f){var g=u.getName(f),v=js(u,f);d.push(i(n(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(n("data.separator.middle"))+n("data.separator.end"),p.push(r)}}),l+=p.join(n("series.multiple.separator.middle"))+n("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},Px=Math.PI,Lx=function(t,e){e=e||{},s(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var i=new Om({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),n=new Nm({shape:{startAngle:-Px/2,endAngle:-Px/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),r=new Om({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});n.animateShape(!0).when(1e3,{endAngle:3*Px/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*Px/2}).delay(300).start("circularInOut");var a=new og;return a.add(n),a.add(r),a.add(i),a.resize=function(){var e=t.getWidth()/2,a=t.getHeight()/2;n.setShape({cx:e,cy:a});var o=n.shape.r;r.setShape({x:e-o,y:a-o,width:2*o,height:2*o}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},a.resize(),a},Ox=pl.prototype;Ox.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},Ox.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,r=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,a=r?i.step:null,o=n&&n.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},Ox.getPipeline=function(t){return this._pipelineMap.get(t)},Ox.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),r=n.count(),a=i.progressiveEnabled&&e.incrementalPrepareRender&&r>=i.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=i.context={progressiveRender:a,modDataCount:s,large:o}},Ox.restorePipelines=function(t){var e=this,i=e._pipelineMap=N();t.eachSeries(function(t){var n=t.getProgressive(),r=t.uid;i.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:n&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(n||700),count:0}),Tl(e,t,t.dataTask)})},Ox.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),i=this.api;f(this._allHandlers,function(n){var r=t.get(n.uid)||t.set(n.uid,[]);n.reset&&vl(this,n,r,e,i),n.overallReset&&ml(this,n,r,e,i)},this)},Ox.prepareView=function(t,e,i,n){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=i,a.api=n,r.__block=!t.incrementalPrepareRender,Tl(this,e,r)},Ox.performDataProcessorTasks=function(t,e){gl(this,this._dataProcessorHandlers,t,e,{block:!0})},Ox.performVisualTasks=function(t,e,i){gl(this,this._visualHandlers,t,e,i)},Ox.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},Ox.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var Ex=Ox.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},zx=Ml(0);pl.wrapStageHandler=function(t,e){return w(t)&&(t={overallReset:t,seriesType:Cl(t)}),t.uid=po("stageHandler"),e&&(t.visualType=e),t};var Bx,Rx={},Nx={};Dl(Rx,Yy),Dl(Nx,_s),Rx.eachSeriesByType=Rx.eachRawSeriesByType=function(t){Bx=t},Rx.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Bx=t.subType)};var Fx=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Vx={color:Fx,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Fx]},Hx="#eee",Wx=function(){return{axisLine:{lineStyle:{color:Hx}},axisTick:{lineStyle:{color:Hx}},axisLabel:{textStyle:{color:Hx}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Hx}}}},Gx=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],Xx={color:Gx,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:Hx},crossStyle:{color:Hx},label:{color:"#000"}}},legend:{textStyle:{color:Hx}},textStyle:{color:Hx},title:{textStyle:{color:Hx}},toolbox:{iconStyle:{normal:{borderColor:Hx}}},dataZoom:{textStyle:{color:Hx}},visualMap:{textStyle:{color:Hx}},timeline:{lineStyle:{color:Hx},itemStyle:{normal:{color:Gx[1]}},label:{normal:{textStyle:{color:Hx}}},controlStyle:{normal:{color:Hx,borderColor:Hx}}},timeAxis:Wx(),logAxis:Wx(),valueAxis:Wx(),categoryAxis:Wx(),line:{symbol:"circle"},graph:{color:Gx},gauge:{title:{textStyle:{color:Hx}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};Xx.categoryAxis.splitLine.show=!1,Ay.extend({type:"dataset",defaultOption:{seriesLayoutBy:Vy,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){ts(this)}}),mx.extend({type:"dataset"});var Yx=Qr.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=.5522848,n=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*i,l=o*i;t.moveTo(n-a,r),t.bezierCurveTo(n-a,r-l,n-s,r-o,n,r-o),t.bezierCurveTo(n+s,r-o,n+a,r-l,n+a,r),t.bezierCurveTo(n+a,r+l,n+s,r+o,n,r+o),t.bezierCurveTo(n-s,r+o,n-a,r+l,n-a,r),t.closePath()}}),Ux=/[\s,]+/;kl.prototype.parse=function(t,e){e=e||{};var i=Al(t);if(!i)throw new Error("Illegal svg");var n=new og;this._root=n;var r=i.getAttribute("viewBox")||"",a=parseFloat(i.getAttribute("width")||e.width),o=parseFloat(i.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),El(i,n,null,!0);for(var s=i.firstChild;s;)this._parseNode(s,n),s=s.nextSibling;var l,h;if(r){var u=E(r).split(Ux);u.length>=4&&(l={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])})}if(l&&null!=a&&null!=o&&(h=Nl(l,a,o),!e.ignoreViewBox)){var c=n;n=new og,n.add(c),c.scale=h.scale.slice(),c.position=h.position.slice()}return e.ignoreRootClip||null==a||null==o||n.setClipPath(new Om({shape:{x:0,y:0,width:a,height:o}})),{root:n,width:a,height:o,viewBoxRect:l,viewBoxTransform:h}},kl.prototype._parseNode=function(t,e){var i=t.nodeName.toLowerCase();"defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0);var n;if(this._isDefine){var r=qx[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{var r=jx[i];r&&(n=r.call(this,t,e),e.add(n))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},kl.prototype._parseText=function(t,e){if(1===t.nodeType){var i=t.getAttribute("dx")||0,n=t.getAttribute("dy")||0;this._textX+=parseFloat(i),this._textY+=parseFloat(n)}var r=new wm({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});Ll(e,r),El(t,r,this._defs);var a=r.style.fontSize;a&&9>a&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=a/9,r.scale[1]*=a/9);var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r};var jx={g:function(t,e){var i=new og;return Ll(e,i),El(t,i,this._defs),i},rect:function(t,e){var i=new Om;return Ll(e,i),El(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new bm;return Ll(e,i),El(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new zm;return Ll(e,i),El(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new Yx;return Ll(e,i),El(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i&&(i=Ol(i));var n=new Am({shape:{points:i||[]}});return Ll(e,n),El(t,n,this._defs),n},polyline:function(t,e){var i=new Qr;
Ll(e,i),El(t,i,this._defs);var n=t.getAttribute("points");n&&(n=Ol(n));var r=new km({shape:{points:n||[]}});return r},image:function(t,e){var i=new In;return Ll(e,i),El(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,n=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(r),this._textY=parseFloat(n)+parseFloat(a);var o=new og;return Ll(e,o),El(t,o,this._defs),o},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=n&&(this._textY=parseFloat(n));var r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new og;return Ll(e,o),El(t,o,this._defs),this._textX+=r,this._textY+=a,o},path:function(t,e){var i=t.getAttribute("d")||"",n=ia(i);return Ll(e,n),El(t,n,this._defs),n}},qx={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),n=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new Hm(e,i,n,r);return Pl(t,a),a},radialgradient:function(){}},Zx={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},Kx=/url\(\s*#(.*?)\)/,$x=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,Qx=/([^\s:;]+)\s*:\s*([^:;]+)/g,Jx=N(),t_={registerMap:function(t,e,i){var n;return _(e)?n=e:e.svg?n=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(i=e.specialAreas,e=e.geoJson),n=[{type:"geoJSON",source:e,specialAreas:i}]),f(n,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var i=e_[e];i(t)}),Jx.set(t,n)},retrieveMap:function(t){return Jx.get(t)}},e_={geoJSON:function(t){var e=t.source;t.geoJSON=b(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=Al(t.source)}},i_=O,n_=f,r_=w,a_=S,o_=Ay.parseClassType,s_="4.6.0",l_={zrender:"4.2.0"},h_=1,u_=1e3,c_=800,d_=900,f_=5e3,p_=1e3,g_=1100,v_=2e3,m_=3e3,y_=3500,x_=4e3,__=5e3,w_={PROCESSOR:{FILTER:u_,SERIES_FILTER:c_,STATISTIC:f_},VISUAL:{LAYOUT:p_,PROGRESSIVE_LAYOUT:g_,GLOBAL:v_,CHART:m_,POST_CHART_LAYOUT:y_,COMPONENT:x_,BRUSH:__}},b_="__flagInMainProcess",S_="__optionUpdated",M_=/^[a-zA-Z0-9_]+$/;Vl.prototype.on=Fl("on",!0),Vl.prototype.off=Fl("off",!0),Vl.prototype.one=Fl("one",!0),c(Vl,gp);var I_=Hl.prototype;I_._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[S_]){var e=this[S_].silent;this[b_]=!0,Gl(this),T_.update.call(this),this[b_]=!1,this[S_]=!1,jl.call(this,e),ql.call(this,e)}else if(t.unfinished){var i=h_,n=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(n),t.performDataProcessorTasks(n),Yl(this,n),t.performVisualTasks(n),th(this,this._model,r,"remain"),i-=+new Date-a}while(i>0&&t.unfinished);t.unfinished||this._zr.flush()}}},I_.getDom=function(){return this._dom},I_.getZr=function(){return this._zr},I_.setOption=function(t,e,i){if(!this._disposed){var n;if(a_(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[b_]=!0,!this._model||e){var r=new bs(this._api),a=this._theme,o=this._model=new Yy;o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,P_),i?(this[S_]={silent:n},this[b_]=!1):(Gl(this),T_.update.call(this),this._zr.flush(),this[S_]=!1,this[b_]=!1,jl.call(this,n),ql.call(this,n))}},I_.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},I_.getModel=function(){return this._model},I_.getOption=function(){return this._model&&this._model.getOption()},I_.getWidth=function(){return this._zr.getWidth()},I_.getHeight=function(){return this._zr.getHeight()},I_.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},I_.getRenderedCanvas=function(t){if(jf.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},I_.getSvgDataUrl=function(){if(jf.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.pathToDataUrl()}},I_.getDataURL=function(t){if(!this._disposed){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;n_(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return n_(n,function(t){t.group.ignore=!1}),a}},I_.getConnectedDataURL=function(t){if(!this._disposed&&jf.canvasSupported){var e=this.group,i=Math.min,r=Math.max,a=1/0;if(R_[e]){var o=a,s=a,l=-a,h=-a,u=[],c=t&&t.pixelRatio||1;f(B_,function(a){if(a.group===e){var c=a.getRenderedCanvas(n(t)),d=a.getDom().getBoundingClientRect();o=i(d.left,o),s=i(d.top,s),l=r(d.right,l),h=r(d.bottom,h),u.push({dom:c,left:d.left,top:d.top})}}),o*=c,s*=c,l*=c,h*=c;var d=l-o,p=h-s,g=rp();g.width=d,g.height=p;var v=Un(g);return t.connectedBackgroundColor&&v.add(new Om({shape:{x:0,y:0,width:d,height:p},style:{fill:t.connectedBackgroundColor}})),n_(u,function(t){var e=new In({style:{x:t.left*c-o,y:t.top*c-s,image:t.dom}});v.add(e)}),v.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},I_.convertToPixel=x(Wl,"convertToPixel"),I_.convertFromPixel=x(Wl,"convertFromPixel"),I_.containPixel=function(t,e){if(!this._disposed){var i,n=this._model;return t=sr(n,t),f(t,function(t,n){n.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(i|=a.containPoint(e,t))}},this)},this),!!i}},I_.getVisual=function(t,e){var i=this._model;t=sr(i,t,{defaultMainType:"series"});var n=t.seriesModel,r=n.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},I_.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},I_.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var T_={prepareAndUpdate:function(t){Gl(this),T_.update.call(this,t)},update:function(t){var e=this._model,i=this._api,n=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,i),a.performDataProcessorTasks(e,t),Yl(this,e),r.update(e,i),$l(e),a.performVisualTasks(e,t),Ql(this,e,i,t);var o=e.get("backgroundColor")||"transparent";if(jf.canvasSupported)n.setBackgroundColor(o);else{var s=Ke(o);o=ai(s,"rgb"),0===s[3]&&(o="transparent")}eh(e,i)}},updateTransform:function(t){var e=this._model,i=this,n=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=i.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,n,t);l&&l.update&&r.push(s)}else r.push(s)});var a=N();e.eachSeries(function(r){var o=i._chartsMap[r.__viewId];if(o.updateTransform){var s=o.updateTransform(r,e,n,t);s&&s.update&&a.set(r.uid,1)}else a.set(r.uid,1)}),$l(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:a}),th(i,e,n,t,a),eh(e,this._api)}},updateView:function(t){var e=this._model;e&&(sl.markUpdateMethod(t,"updateView"),$l(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Ql(this,this._model,this._api,t),eh(e,this._api))},updateVisual:function(t){T_.update.call(this,t)},updateLayout:function(t){T_.update.call(this,t)}};I_.resize=function(t){if(!this._disposed){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var i=e.resetOption("media"),n=t&&t.silent;this[b_]=!0,i&&Gl(this),T_.update.call(this),this[b_]=!1,jl.call(this,n),ql.call(this,n)}}},I_.showLoading=function(t,e){if(!this._disposed&&(a_(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),z_[t])){var i=z_[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},I_.hideLoading=function(){this._disposed||(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},I_.makeActionFromEvent=function(t){var e=o({},t);return e.type=A_[t.type],e},I_.dispatchAction=function(t,e){if(!this._disposed&&(a_(e)||(e={silent:!!e}),D_[t.type]&&this._model)){if(this[b_])return void this._pendingActions.push(t);Ul.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&jf.browser.weChat&&this._throttledZrFlush(),jl.call(this,e.silent),ql.call(this,e.silent)}},I_.appendData=function(t){if(!this._disposed){var e=t.seriesIndex,i=this.getModel(),n=i.getSeriesByIndex(e);n.appendData(t),this._scheduler.unfinished=!0}},I_.on=Fl("on",!1),I_.off=Fl("off",!1),I_.one=Fl("one",!1);var C_=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];I_._initEvents=function(){n_(C_,function(t){var e=function(e){var i,n=this.getModel(),r=e.target,a="globalout"===t;if(a)i={};else if(r&&null!=r.dataIndex){var s=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(i=o({},r.eventData));if(i){var l=i.componentType,h=i.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",h=i.seriesIndex);var u=l&&null!=h&&n.getComponent(l,h),c=u&&this["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];i.event=e,i.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:i,model:u,view:c},this.trigger(t,i)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),n_(A_,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},I_.isDisposed=function(){return this._disposed},I_.clear=function(){this._disposed||this.setOption({series:[]},!0)},I_.dispose=function(){if(!this._disposed){this._disposed=!0,hr(this.getDom(),V_,"");var t=this._api,e=this._model;n_(this._componentsViews,function(i){i.dispose(e,t)}),n_(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete B_[this.id]}},c(Hl,gp),oh.prototype={constructor:oh,normalizeQuery:function(t){var e={},i={},n={};if(b(t)){var r=o_(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],o={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<a.length;l++){var h=a[l],u=r.lastIndexOf(h);if(u>0&&u===r.length-h.length){var c=r.slice(0,u);"data"!==c&&(e.mainType=c,e[h.toLowerCase()]=t,s=!0)}}o.hasOwnProperty(r)&&(i[r]=t,s=!0),s||(n[r]=t)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},filter:function(t,e){function i(t,e,i,n){return null==t[i]||e[n||i]===t[i]}var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var l=e.cptQuery,h=e.dataQuery;return i(l,o,"mainType")&&i(l,o,"subType")&&i(l,o,"index","componentIndex")&&i(l,o,"name")&&i(l,o,"id")&&i(h,a,"name")&&i(h,a,"dataIndex")&&i(h,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a))},afterTrigger:function(){this.eventInfo=null}};var D_={},A_={},k_=[],P_=[],L_=[],O_=[],E_={},z_={},B_={},R_={},N_=new Date-0,F_=new Date-0,V_="_echarts_instance_",H_=uh;bh(v_,Dx),gh(ax),vh(d_,ox),Mh("default",Lx),yh({type:"highlight",event:"highlight",update:"highlight"},V),yh({type:"downplay",event:"downplay",update:"downplay"},V),ph("light",Vx),ph("dark",Xx);var W_={};Oh.prototype={constructor:Oh,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,i=this._new,n={},r={},a=[],o=[];for(Eh(e,n,a,"_oldKeyGetter",this),Eh(i,r,o,"_newKeyGetter",this),t=0;t<e.length;t++){var s=a[t],l=r[s];if(null!=l){var h=l.length;h?(1===h&&(r[s]=null),l=l.shift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<o.length;t++){var s=o[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var u=0,h=l.length;h>u;u++)this._add&&this._add(l[u]);else this._add&&this._add(l)}}}};var G_=N(["tooltip","label","itemName","itemId","seriesName"]),X_=S,Y_="undefined",U_=-1,j_="e\x00\x00",q_={"float":typeof Float64Array===Y_?Array:Float64Array,"int":typeof Int32Array===Y_?Array:Int32Array,ordinal:Array,number:Array,time:Array},Z_=typeof Uint32Array===Y_?Array:Uint32Array,K_=typeof Int32Array===Y_?Array:Int32Array,$_=typeof Uint16Array===Y_?Array:Uint16Array,Q_=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],J_=["_extent","_approximateExtent","_rawExtent"],tw=function(t,e){t=t||["x","y"];for(var i={},n=[],r={},a=0;a<t.length;a++){var o=t[a];b(o)?o=new Fh({name:o}):o instanceof Fh||(o=new Fh(o));var s=o.name;o.type=o.type||"float",o.coordDim||(o.coordDim=s,o.coordDimIndex=0),o.otherDims=o.otherDims||{},n.push(s),i[s]=o,o.index=a,o.createInvertedIndices&&(r[s]=[])}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=zh(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},ew=tw.prototype;ew.type="list",ew.hasItemOption=!0,ew.getDimension=function(t){return("number"==typeof t||!isNaN(t)&&!this._dimensionInfos.hasOwnProperty(t))&&(t=this.dimensions[t]),t},ew.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},ew.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},ew.mapDimension=function(t,e){var i=this._dimensionsSummary;if(null==e)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return e===!0?(n||[]).slice():n&&n[e]},ew.initData=function(t,e,i){var n=Jo.isInstance(t)||d(t);n&&(t=new Vs(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},i||(this.hasItemOption=!1),this.defaultDimValueGetter=ux[this._rawData.getSource().sourceFormat],this._dimValueGetter=i=i||this.defaultDimValueGetter,this._dimValueGetterArrayRows=ux.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},ew.getProvider=function(){return this._rawData},ew.appendData=function(t){var e=this._rawData,i=this.count();e.appendData(t);var n=e.count();e.persistent||(n+=i),this._initDataFromProvider(i,n)},ew.appendValues=function(t,e){for(var i=this._chunkSize,n=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;a>u;u++){var c=r[u];o[c]||(o[c]=Qh()),n[c]||(n[c]=[]),Gh(n,this._dimensionInfos[c],i,h,l),this._chunkCount=n[c].length}for(var d=new Array(a),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/i),v=f%i,m=0;a>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);n[c][g][v]=y;var x=o[c];y<x[0]&&(x[0]=y),y>x[1]&&(x[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},Xh(this)},ew._initDataFromProvider=function(t,e){if(!(t>=e)){for(var i,n=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=o[p];c[g]||(c[g]=Qh());var v=l[g];0===v.otherDims.itemName&&(i=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),Gh(a,v,n,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var x=Math.floor(y/n),_=y%n,w=0;s>w;w++){var g=o[w],b=a[g][x],S=this._dimValueGetter(m,g,y,w);b[_]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var I=h[y];if(m&&null==I)if(null!=m.name)h[y]=I=m.name;else if(null!=i){var T=o[i],C=a[T][x];if(C){I=C[_];var D=l[T].ordinalMeta;D&&D.categories.length&&(I=D.categories[I])}}var A=null==m?null:m.id;null==A&&null!=I&&(d[I]=d[I]||0,A=I,d[I]>0&&(A+="__ec__"+d[I]),d[I]++),null!=A&&(u[y]=A)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},Xh(this)}},ew.count=function(){return this._count},ew.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var r=0;n>r;r++)t[r]=e[r]}else t=new i(e.buffer,0,n)}else for(var i=Vh(this),t=new i(this.count()),r=0;r<t.length;r++)t[r]=r;return t},ew.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var i=this._storage;if(!i[t])return 0/0;e=this.getRawIndex(e);var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[t][n],o=a[r];return o},ew.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var i=this._storage[t];if(!i)return 0/0;var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[n];return a[r]},ew._getFast=function(t,e){var i=Math.floor(e/this._chunkSize),n=e%this._chunkSize,r=this._storage[t][i];return r[n]},ew.getValues=function(t,e){var i=[];_(t)||(e=t,t=this.dimensions);for(var n=0,r=t.length;r>n;n++)i.push(this.get(t[n],e));return i},ew.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,i=0,n=e.length;n>i;i++)if(isNaN(this.get(e[i],t)))return!1;return!0},ew.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],i=Qh();if(!e)return i;var n,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();n=i;for(var o=n[0],s=n[1],l=0;r>l;l++){var h=this._getFast(t,this.getRawIndex(l));o>h&&(o=h),h>s&&(s=h)}return n=[o,s],this._extent[t]=n,n},ew.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},ew.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},ew.getCalculationInfo=function(t){return this._calculationInfo[t]},ew.setCalculationInfo=function(t,e){X_(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},ew.getSum=function(t){var e=this._storage[t],i=0;if(e)for(var n=0,r=this.count();r>n;n++){var a=this.get(t,n);isNaN(a)||(i+=a)}return i},ew.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var i=[].concat(e).sort(function(t,e){return t-e}),n=this.count();return 0===n?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},ew.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i[e];return null==n||isNaN(n)?U_:n},ew.indexOfName=function(t){for(var e=0,i=this.count();i>e;e++)if(this.getName(e)===t)return e;return-1},ew.indexOfRawIndex=function(t){if(t>=this._rawCount||0>t)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(null!=i&&i<this._count&&i===t)return t;for(var n=0,r=this._count-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},ew.indicesOfNearest=function(t,e,i){var n=this._storage,r=n[t],a=[];if(!r)return a;null==i&&(i=1/0);for(var o=1/0,s=-1,l=0,h=0,u=this.count();u>h;h++){var c=e-this.get(t,h),d=Math.abs(c);i>=d&&((o>d||d===o&&c>=0&&0>s)&&(o=d,s=c,l=0),c===s&&(a[l++]=h))}return a.length=l,a},ew.getRawIndex=Uh,ew.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],i=0;i<this.dimensions.length;i++){var n=this.dimensions[i];e.push(this.get(n,t))}return e},ew.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||Yh(this,this._nameDimIdx,e)||""},ew.getId=function(t){return qh(this,this.getRawIndex(t))},ew.each=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Zh(t),this.getDimension,this);for(var r=t.length,a=0;a<this.count();a++)switch(r){case 0:e.call(i,a);break;case 1:e.call(i,this.get(t[0],a),a);break;case 2:e.call(i,this.get(t[0],a),this.get(t[1],a),a);break;default:for(var o=0,s=[];r>o;o++)s[o]=this.get(t[o],a);s[o]=a,e.apply(i,s)}}},ew.filterSelf=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Zh(t),this.getDimension,this);for(var r=this.count(),a=Vh(this),o=new a(r),s=[],l=t.length,h=0,u=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(i,c);else if(1===l){var g=this._getFast(u,f);d=e.call(i,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(u,f);s[v]=c,d=e.apply(i,s)}d&&(o[h++]=f)}return r>h&&(this._indices=o),this._count=h,this._extent={},this.getRawIndex=this._indices?jh:Uh,this}},ew.selectRange=function(t){if(this._count){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);var n=e.length;if(n){var r=this.count(),a=Vh(this),o=new a(r),s=0,l=e[0],h=t[l][0],u=t[l][1],c=!1;if(!this._indices){var d=0;if(1===n){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=h&&u>=y||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===n){for(var f=this._storage[l],x=this._storage[e[1]],_=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=x[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],S=b[m];(y>=h&&u>=y||isNaN(y))&&(S>=_&&w>=S||isNaN(S))&&(o[s++]=d),d++}c=!0}}if(!c)if(1===n)for(var m=0;r>m;m++){var M=this.getRawIndex(m),y=this._getFast(l,M);(y>=h&&u>=y||isNaN(y))&&(o[s++]=M)}else for(var m=0;r>m;m++){for(var I=!0,M=this.getRawIndex(m),p=0;n>p;p++){var T=e[p],y=this._getFast(i,M);(y<t[T][0]||y>t[T][1])&&(I=!1)}I&&(o[s++]=this.getRawIndex(m))}return r>s&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?jh:Uh,this}}},ew.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i),r},ew.map=function(t,e,i,n){i=i||n||this,t=p(Zh(t),this.getDimension,this);var r=Kh(this,t);r._indices=this._indices,r.getRawIndex=r._indices?jh:Uh;for(var a=r._storage,o=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;h>d;d++){for(var f=0;l>f;f++)u[f]=this.get(t[f],d);u[l]=d;var g=e&&e.apply(i,u);if(null!=g){"object"!=typeof g&&(o[0]=g,g=o);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,x=0;x<g.length;x++){var _=t[x],w=g[x],b=c[_],S=a[_];S&&(S[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},ew.downSample=function(t,e,i,n){for(var r=Kh(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(Vh(this))(h),f=0,p=0;h>p;p+=s){s>h-p&&(s=h-p,o.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/u),y=v%u;o[g]=l[m][y]}var x=i(o),_=this.getRawIndex(Math.min(p+n(o,x)||0,h-1)),w=Math.floor(_/u),b=_%u;l[w][b]=x,x<c[0]&&(c[0]=x),x>c[1]&&(c[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=jh,r},ew.getItemModel=function(t){var e=this.hostModel;return new uo(this.getRawDataItem(t),e,e&&e.ecModel)},ew.diff=function(t){var e=this;return new Oh(t?t.getIndices():[],this.getIndices(),function(e){return qh(t,e)},function(t){return qh(e,t)})},ew.getVisual=function(t){var e=this._visual;return e&&e[t]},ew.setVisual=function(t,e){if(X_(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},ew.setLayout=function(t,e){if(X_(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},ew.getLayout=function(t){return this._layout[t]},ew.getItemLayout=function(t){return this._itemLayouts[t]},ew.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?o(this._itemLayouts[t]||{},e):e},ew.clearItemLayouts=function(){this._itemLayouts.length=0},ew.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},ew.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=n,X_(e))for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a],r[a]=!0);else n[e]=i,r[e]=!0},ew.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var iw=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};ew.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(iw,e)),this._graphicEls[t]=e},ew.getItemGraphicEl=function(t){return this._graphicEls[t]},ew.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},ew.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new tw(e,this.hostModel)}if(t._storage=this._storage,Wh(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?jh:Uh,t},ew.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(P(arguments)))})},ew.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],ew.CHANGABLE_METHODS=["filterSelf","selectRange"];var nw=function(t,e){return e=e||{},Jh(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})},rw={cartesian2d:function(t,e,i,n){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],i.set("x",r),i.set("y",a),ru(r)&&(n.set("x",r),e.firstCategoryDimIndex=0),ru(a)&&(n.set("y",a),null==e.firstCategoryDimIndex&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,i,n){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],i.set("single",r),ru(r)&&(n.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,i,n){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],i.set("radius",a),i.set("angle",o),ru(a)&&(n.set("radius",a),e.firstCategoryDimIndex=0),ru(o)&&(n.set("angle",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,i,n){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),o=e.coordSysDims=a.dimensions.slice();f(a.parallelAxisIndex,function(t,a){var s=r.getComponent("parallelAxis",t),l=o[a];i.set(l,s),ru(s)&&null==e.firstCategoryDimIndex&&(n.set(l,s),e.firstCategoryDimIndex=a)})}};cu.prototype.parse=function(t){return t},cu.prototype.getSetting=function(t){return this._setting[t]},cu.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},cu.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},cu.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},cu.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},cu.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},cu.prototype.getExtent=function(){return this._extent.slice()},cu.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},cu.prototype.isBlank=function(){return this._isBlank},cu.prototype.setBlank=function(t){this._isBlank=t},cu.prototype.getLabel=null,pr(cu),yr(cu,{registerWhenExtend:!0}),du.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&p(i,pu);return new du({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})};var aw=du.prototype;aw.getOrdinal=function(t){return fu(this).get(t)},aw.parseAndCollect=function(t){var e,i=this._needCollect;if("string"!=typeof t&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=fu(this);return e=n.get(t),null==e&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=0/0),e};var ow=cu.prototype,sw=cu.extend({type:"ordinal",init:function(t,e){(!t||_(t))&&(t=new du({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),ow.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return ow.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(ow.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:V,niceExtent:V});sw.create=function(){return new sw};var lw=_o,hw=_o,uw=cu.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),uw.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=vu(t)},getTicks:function(t){var e=this._interval,i=this._extent,n=this._niceExtent,r=this._intervalPrecision,a=[];if(!e)return a;var o=1e4;i[0]<n[0]&&a.push(t?hw(n[0]-e):i[0]);for(var s=n[0];s<=n[1]&&(a.push(s),s=hw(s+e,r),s!==a[a.length-1]);)if(a.length>o)return[];var l=a.length?a[a.length-1]:n[1];return i[1]>l&&a.push(t?l+e:i[1]),a},getMinorTicks:function(t){for(var e=this.getTicks(!0),i=[],n=this.getExtent(),r=1;r<e.length;r++){for(var a=e[r],o=e[r-1],s=0,l=[],h=a-o,u=h/t;t-1>s;){var c=_o(o+(s+1)*u);c>n[0]&&c<n[1]&&l.push(c),s++}i.push(l)}return i},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=So(t)||0:"auto"===i&&(i=this._intervalPrecision),t=hw(t,i,!0),zo(t)},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){0>r&&(r=-r,n.reverse());var a=gu(n,t,e,i);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax?e[0]-=i/2:(e[1]+=i/2,e[0]-=i/2)}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=hw(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=hw(Math.ceil(e[1]/r)*r))}});uw.create=function(){return new uw};var cw="__ec_stack_",dw=.5,fw="undefined"!=typeof Float32Array?Float32Array:Array,pw={seriesType:"bar",plan:xx(),reset:function(t){function e(t,e){for(var i,c=t.count,d=new fw(2*c),f=new fw(c),p=[],g=[],v=0,m=0;null!=(i=t.next());)g[h]=e.get(o,i),g[1-h]=e.get(s,i),p=n.dataToPoint(g,null,p),d[v++]=p[0],d[v++]=p[1],f[m++]=i;e.setLayout({largePoints:d,largeDataIndices:f,barWidth:u,valueAxisStart:Au(r,a,!1),valueAxisHorizontal:l})}if(Cu(t)&&Du(t)){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=n.getOtherAxis(r),o=i.mapDimension(a.dim),s=i.mapDimension(r.dim),l=a.isHorizontal(),h=l?0:1,u=Iu(Su([t]),r,t).width;return u>dw||(u=dw),{progress:e}}}},gw=uw.prototype,vw=Math.ceil,mw=Math.floor,yw=1e3,xw=60*yw,_w=60*xw,ww=24*_w,bw=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][1]<e?i=r+1:n=r}return i},Sw=uw.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return Wo(e[0],i,this.getSetting("useUTC"))
},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=ww,e[1]+=ww),e[1]===-1/0&&1/0===e[0]){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-ww}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=_o(mw(e[0]/n)*n)),t.fixMax||(e[1]=_o(vw(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],a=r/t;null!=e&&e>a&&(a=e),null!=i&&a>i&&(a=i);var o=Mw.length,s=bw(Mw,a,0,o),l=Mw[Math.min(s,o-1)],h=l[1];if("year"===l[0]){var u=r/h,c=Po(u/t,!0);h*=c}var d=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,f=[Math.round(vw((n[0]-d)/h)*h+d),Math.round(mw((n[1]-d)/h)*h+d)];yu(f,n),this._stepLvl=l,this._interval=h,this._niceExtent=f},parse:function(t){return+Do(t)}});f(["contain","normalize"],function(t){Sw.prototype[t]=function(e){return gw[t].call(this,this.parse(e))}});var Mw=[["hh:mm:ss",yw],["hh:mm:ss",5*yw],["hh:mm:ss",10*yw],["hh:mm:ss",15*yw],["hh:mm:ss",30*yw],["hh:mm\nMM-dd",xw],["hh:mm\nMM-dd",5*xw],["hh:mm\nMM-dd",10*xw],["hh:mm\nMM-dd",15*xw],["hh:mm\nMM-dd",30*xw],["hh:mm\nMM-dd",_w],["hh:mm\nMM-dd",2*_w],["hh:mm\nMM-dd",6*_w],["hh:mm\nMM-dd",12*_w],["MM-dd\nyyyy",ww],["MM-dd\nyyyy",2*ww],["MM-dd\nyyyy",3*ww],["MM-dd\nyyyy",4*ww],["MM-dd\nyyyy",5*ww],["MM-dd\nyyyy",6*ww],["week",7*ww],["MM-dd\nyyyy",10*ww],["week",14*ww],["week",21*ww],["month",31*ww],["week",42*ww],["month",62*ww],["week",70*ww],["quarter",95*ww],["month",31*ww*4],["month",31*ww*5],["half-year",380*ww/2],["month",31*ww*8],["month",31*ww*10],["year",380*ww]];Sw.create=function(t){return new Sw({useUTC:t.ecModel.get("useUTC")})};var Iw=cu.prototype,Tw=uw.prototype,Cw=So,Dw=_o,Aw=Math.floor,kw=Math.ceil,Pw=Math.pow,Lw=Math.log,Ow=cu.extend({type:"log",base:10,$constructor:function(){cu.apply(this,arguments),this._originalScale=new uw},getTicks:function(t){var e=this._originalScale,i=this._extent,n=e.getExtent();return p(Tw.getTicks.call(this,t),function(t){var r=_o(Pw(this.base,t));return r=t===i[0]&&e.__fixMin?ku(r,n[0]):r,r=t===i[1]&&e.__fixMax?ku(r,n[1]):r},this)},getMinorTicks:Tw.getMinorTicks,getLabel:Tw.getLabel,scale:function(t){return t=Iw.scale.call(this,t),Pw(this.base,t)},setExtent:function(t,e){var i=this.base;t=Lw(t)/Lw(i),e=Lw(e)/Lw(i),Tw.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=Iw.getExtent.call(this);e[0]=Pw(t,e[0]),e[1]=Pw(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=ku(e[0],n[0])),i.__fixMax&&(e[1]=ku(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Lw(t[0])/Lw(e),t[1]=Lw(t[1])/Lw(e),Iw.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=Ao(i),r=t/i*n;for(.5>=r&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var a=[_o(kw(e[0]/n)*n),_o(Aw(e[1]/n)*n)];this._interval=n,this._niceExtent=a}},niceExtent:function(t){Tw.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){Ow.prototype[t]=function(e){return e=Lw(e)/Lw(this.base),Iw[t].call(this,e)}}),Ow.create=function(){return new Ow};var Ew={getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:V,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},zw=da({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),Bw=da({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),Rw=da({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=n-a+o+s,h=Math.asin(s/o),u=Math.cos(h)*o,c=Math.sin(h),d=Math.cos(h),f=.6*o,p=.7*o;t.moveTo(i-u,l+s),t.arc(i,l,o,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(i+u-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-u+c*f,l+s+d*f,i-u,l+s),t.closePath()}}),Nw=da({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),Fw={line:zm,rect:Om,roundRect:Om,square:Om,circle:bm,diamond:Bw,pin:Rw,arrow:Nw,triangle:zw},Vw={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},Hw={};f(Fw,function(t,e){Hw[e]=new t});var Ww=da({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,i){var n=ji(t,e,i),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(n.y=i.y+.4*i.height),n},buildPath:function(t,e,i){var n=e.symbolType;if("none"!==n){var r=Hw[n];r||(n="rect",r=Hw[n]),Vw[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i)}}}),Gw={isDimensionStacked:ou,enableDataStack:au,getStackedDimension:su},Xw=(Object.freeze||Object)({createList:Xu,getLayoutRect:jo,dataStack:Gw,createScale:Yu,mixinAxisModelCommonMethods:Uu,completeDimensions:Jh,createDimensions:nw,createSymbol:Gu}),Yw=1e-8;Zu.prototype={constructor:Zu,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,i=[e,e],n=[-e,-e],r=[],a=[],o=this.geometries,s=0;s<o.length;s++)if("polygon"===o[s].type){var l=o[s].exterior;Or(l,r,a),oe(i,i,r),se(n,n,a)}return 0===s&&(i[0]=i[1]=n[0]=n[1]=0),this._rect=new bi(i[0],i[1],n[0]-i[0],n[1]-i[1])},contain:function(t){var e=this.getBoundingRect(),i=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var n=0,r=i.length;r>n;n++)if("polygon"===i[n].type){var a=i[n].exterior,o=i[n].interiors;if(qu(a,t[0],t[1])){for(var s=0;s<(o?o.length:0);s++)if(qu(o[s]))continue t;return!0}}return!1},transformTo:function(t,e,i,n){var r=this.getBoundingRect(),a=r.width/r.height;i?n||(n=i/a):i=a*n;for(var o=new bi(t,e,i,n),s=r.calculateTransform(o),l=this.geometries,h=0;h<l.length;h++)if("polygon"===l[h].type){for(var u=l[h].exterior,c=l[h].interiors,d=0;d<u.length;d++)ae(u[d],u[d],s);for(var f=0;f<(c?c.length:0);f++)for(var d=0;d<c[f].length;d++)ae(c[f][d],c[f][d],s)}r=this._rect,r.copy(o),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new Zu(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var Uw=function(t){return Ku(t),p(v(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var e=t.properties,i=t.geometry,n=i.coordinates,r=[];"Polygon"===i.type&&r.push({type:"polygon",exterior:n[0],interiors:n.slice(1)}),"MultiPolygon"===i.type&&f(n,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var a=new Zu(e.name,r,e.cp);return a.properties=e,a})},jw=or(),qw=[0,1],Zw=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};Zw.prototype={constructor:Zw,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Mo(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&(i=i.slice(),dc(i,n.count())),yo(t,qw,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&(i=i.slice(),dc(i,n.count()));var r=yo(t,i,qw,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=Ju(this,e),n=i.ticks,r=p(n,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),a=e.get("alignWithLabel");return fc(this,r,a,t.clamp),r},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&100>e||(e=5);var i=this.scale.getMinorTicks(e),n=p(i,function(t){return p(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this);return n},getViewLabels:function(){return Qu(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return lc(this)}};var Kw=Uw,$w={};f(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){$w[t]=sp[t]});var Qw={};f(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){Qw[t]=ry[t]});var Jw=function(t){this._axes={},this._dimList=[],this.name=t||""};Jw.prototype={constructor:Jw,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,pc,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},gc.prototype={constructor:gc,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,i){var n=this.getAxis("x"),r=this.getAxis("y");return i=i||[],i[0]=n.toGlobalCoord(n.dataToCoord(t[0])),i[1]=r.toGlobalCoord(r.dataToCoord(t[1])),i},clampData:function(t,e){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,r=i.getExtent(),a=n.getExtent(),o=i.parse(t[0]),s=n.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return e=e||[],e[0]=i.coordToData(i.toLocalCoord(t[0])),e[1]=n.coordToData(n.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),i=Math.min(t[0],t[1]),n=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-i,a=Math.max(e[0],e[1])-n,o=new bi(i,n,r,a);return o}},u(gc,Jw);var tb=function(t,e,i,n,r){Zw.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"};tb.prototype={constructor:tb,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},u(tb,Zw);var eb={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},ib={};ib.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},eb),ib.valueAxis=r({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},eb),ib.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},ib.valueAxis),ib.logAxis=s({scale:!0,logBase:10},ib.valueAxis);var nb=["value","category","time","log"],rb=function(t,e,i,n){f(nb,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,n){var a=this.layoutMode,s=a?Zo(e):{},l=n.getTheme();r(e,l.get(o+"Axis")),r(e,this.getDefaultOption()),e.type=i(t,e),a&&qo(e,s,a)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=du.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:a([{},ib[o+"Axis"],n],!0)})}),Ay.registerSubTypeDefaulter(t+"Axis",x(i,t))},ab=Ay.extend({type:"cartesian2dAxis",axis:null,init:function(){ab.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){ab.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){ab.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(ab.prototype,Ew);var ob={offset:0};rb("x",ab,vc,ob),rb("y",ab,vc,ob),Ay.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var sb=yc.prototype;sb.type="grid",sb.axisPointerEnabled=!0,sb.getRect=function(){return this._rect},sb.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),f(i.x,function(t){Ou(t.scale,t.model)}),f(i.y,function(t){Ou(t.scale,t.model)});var n={};f(i.x,function(t){xc(i,"y",t,n)}),f(i.y,function(t){xc(i,"x",t,n)}),this.resize(this.model,e)},sb.resize=function(t,e,i){function n(){f(a,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),wc(t,e?r.x:r.y)})}var r=jo(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var a=this._axesList;n(),!i&&t.get("containLabel")&&(f(a,function(t){if(!t.model.get("axisLabel.inside")){var e=Nu(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),n())},sb.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},sb.getAxes=function(){return this._axesList.slice()},sb.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}S(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},sb.getCartesians=function(){return this._coordsList.slice()},sb.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},sb.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},sb._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r.coordinateSystem,h(l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},sb.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},sb._initCartesian=function(t,e){function i(i){return function(o,s){if(mc(o,t,e)){var l=o.get("position");"x"===i?"top"!==l&&"bottom"!==l&&(l=n.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=n.left?"right":"left"),n[l]=!0;var h=new tb(i,Eu(o),[0,0],o.get("type"),l),u="category"===h.type;h.onBand=u&&o.get("boundaryGap"),h.inverse=o.get("inverse"),o.axis=h,h.model=o,h.grid=this,h.index=s,this._axesList.push(h),r[i][s]=h,a[i]++}}}var n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};return e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),a.x&&a.y?(this._axesMap=r,void f(r.x,function(e,i){f(r.y,function(n,r){var a="x"+i+"y"+r,o=new gc(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(n)},this)},this)):(this._axesMap={},void(this._axesList=[]))},sb._updateScale=function(t,e){function i(t,e){f(t.mapDimension(e.dim,!0),function(i){e.scale.unionExtentFromData(t,su(t,i))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(n){if(Sc(n)){var r=bc(n,t),a=r[0],o=r[1];if(!mc(a,e,t)||!mc(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=n.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(i(l,h,n),i(l,u,n))}},this)},sb.getTooltipAxes=function(t){var e=[],i=[];return f(this.getCartesians(),function(n){var r=null!=t&&"auto"!==t?n.getAxis(t):n.getBaseAxis(),a=n.getOtherAxis(r);h(e,r)<0&&e.push(r),h(i,a)<0&&i.push(a)}),{baseAxes:e,otherAxes:i}};var lb=["xAxis","yAxis"];yc.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,r){var a=new yc(n,t,e);a.name="grid_"+r,a.resize(n,e,!0),n.coordinateSystem=a,i.push(a)}),t.eachSeries(function(e){if(Sc(e)){var i=bc(e,t),n=i[0],r=i[1],a=n.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(n.componentIndex,r.componentIndex)}}),i},yc.dimensions=yc.prototype.dimensions=gc.prototype.dimensions,ws.register("cartesian2d",yc);var hb=vx.extend({type:"series.__base_bar__",getInitialData:function(){return lu(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(e.clampData(t)),n=this.getData(),r=n.getLayout("offset"),a=n.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});hb.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1}});var ub=pv([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),cb={getBarItemStyle:function(t){var e=ub(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},db=da({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=.5*(a-r),s=r+o,l=e.startAngle,h=e.endAngle,u=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(h),p=Math.sin(h),g=u?h-l<2*Math.PI:l-h<2*Math.PI;g&&(t.moveTo(c*r+i,d*r+n),t.arc(c*s+i,d*s+n,o,-Math.PI+l,l,!u)),t.arc(i,n,a,l,h,!u),t.moveTo(f*a+i,p*a+n),t.arc(f*s+i,p*s+n,o,h-2*Math.PI,h-Math.PI,!u),0!==r&&(t.arc(i,n,r,h,l,u),t.moveTo(c*r+i,p*r+n)),t.closePath()}}),fb=["itemStyle","barBorderWidth"],pb=[0,0];o(uo.prototype,cb),Dh({type:"bar",render:function(t,e,i){this._updateDrawMode(t);var n=t.get("coordinateSystem");return("cartesian2d"===n||"polar"===n)&&(this._isLargeDraw?this._renderLarge(t,e,i):this._renderNormal(t,e,i)),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,i=this.group,n=t.getData(),r=this._data,a=t.coordinateSystem,o=a.getBaseAxis();"cartesian2d"===a.type?e=o.isHorizontal():"polar"===a.type&&(e="angle"===o.dim);var s=t.isAnimationEnabled()?t:null,l=t.get("clip",!0),h=kc(a,n);i.removeClipPath();var u=t.get("roundCap",!0);n.diff(r).add(function(r){if(n.hasValue(r)){var o=n.getItemModel(r),c=xb[a.type](n,r,o);if(l){var d=mb[a.type](h,c);if(d)return void i.remove(f)}var f=yb[a.type](r,c,e,s,!1,u);n.setItemGraphicEl(r,f),i.add(f),Ec(f,n,r,o,c,t,e,"polar"===a.type)}}).update(function(o,c){var d=r.getItemGraphicEl(c);if(!n.hasValue(o))return void i.remove(d);var f=n.getItemModel(o),p=xb[a.type](n,o,f);if(l){var g=mb[a.type](h,p);if(g)return void i.remove(d)}d?$a(d,{shape:p},s,o):d=yb[a.type](o,p,e,s,!0,u),n.setItemGraphicEl(o,d),i.add(d),Ec(d,n,o,f,p,t,e,"polar"===a.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===a.type?e&&Pc(t,s,e):e&&Lc(t,s,e)}).execute(),this._data=n},_renderLarge:function(t){this._clear(),Bc(t,this.group);var e=t.get("clip",!0)?Ac(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){Bc(e,this.group,!0)},dispose:V,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,i=this._data;t&&t.get("animation")&&i&&!this._isLargeDraw?i.eachItemGraphicEl(function(e){"sector"===e.type?Lc(e.dataIndex,t,e):Pc(e.dataIndex,t,e)}):e.removeAll(),this._data=null}});var gb=Math.max,vb=Math.min,mb={cartesian2d:function(t,e){var i=e.width<0?-1:1,n=e.height<0?-1:1;0>i&&(e.x+=e.width,e.width=-e.width),0>n&&(e.y+=e.height,e.height=-e.height);var r=gb(e.x,t.x),a=vb(e.x+e.width,t.x+t.width),o=gb(e.y,t.y),s=vb(e.y+e.height,t.y+t.height);e.x=r,e.y=o,e.width=a-r,e.height=s-o;var l=e.width<0||e.height<0;return 0>i&&(e.x+=e.width,e.width=-e.width),0>n&&(e.y+=e.height,e.height=-e.height),l},polar:function(){return!1}},yb={cartesian2d:function(t,e,i,n,r){var a=new Om({shape:o({},e)});if(n){var s=a.shape,l=i?"height":"width",h={};s[l]=0,h[l]=e[l],ry[r?"updateProps":"initProps"](a,{shape:h},n,t)}return a},polar:function(t,e,i,n,r,a){var o=e.startAngle<e.endAngle,l=!i&&a?db:Im,h=new l({shape:s({clockwise:o},e)});if(n){var u=h.shape,c=i?"r":"endAngle",d={};u[c]=i?0:e.startAngle,d[c]=e[c],ry[r?"updateProps":"initProps"](h,{shape:d},n,t)}return h}},xb={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=zc(i,n),a=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+a*r/2,y:n.y+o*r/2,width:n.width-a*r,height:n.height-o*r}},polar:function(t,e){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}},_b=Qr.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var i=e.points,n=this.__startPoint,r=this.__baseDimIdx,a=0;a<i.length;a+=2)n[r]=i[a+r],t.moveTo(n[0],n[1]),t.lineTo(i[a],i[a+1])}}),wb=dl(function(t){var e=this,i=Rc(e,t.offsetX,t.offsetY);e.dataIndex=i>=0?i:null},30,!1),bb=Math.PI,Sb=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new og;var i=new og({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};Sb.prototype={constructor:Sb,hasBuilder:function(t){return!!Mb[t]},add:function(t){Mb[t].call(this)},getGroup:function(){return this.group}};var Mb={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis.getExtent(),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(ae(r,r,n),ae(a,a,n));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new zm({anid:"line",subPixelOptimize:!0,shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1}));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),u=e.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof h||"number"==typeof h)&&(h=[h,h]);var c=h[0],d=h[1];f([{rotate:t.rotation+Math.PI/2,offset:u[0],r:0},{rotate:t.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,i){if("none"!==l[i]&&null!=l[i]){var n=Gu(l[i],-c/2,-d/2,c,d,s.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];n.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(n)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=Yc(this,t,e),n=jc(this,t,e);Vc(t,n,i),Uc(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,i=D(t.axisName,e.get("name"));if(i){var n,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,Gc(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*bb/180);var f;Gc(r)?n=Tb(t.rotation,null!=d?d:t.rotation,a):(n=Fc(t,r,d||0,h),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(n.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=D(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?wy(i,m,p,v,{minChar:2,placeholder:g.placeholder}):i,x=e.get("tooltip",!0),_=e.mainType,w={componentType:_,name:i,$vars:["name"]};w[_+"Index"]=e.componentIndex;var b=new wm({anid:"name",__fullText:i,__truncatedText:y,position:c,rotation:n.rotation,silent:Cb(e),z2:1,tooltip:x&&x.show?o({content:i,formatter:function(){return i},formatterParams:w},x):null});Ha(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||n.textAlign,textVerticalAlign:s.get("verticalAlign")||n.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=Ib(e),b.eventData.targetType="axisName",b.eventData.name=i),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},Ib=Sb.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},Tb=Sb.innerTextLayout=function(t,e,i){var n,r,a=To(e-t);return Co(a)?(r=i>0?"top":"bottom",n="center"):Co(a-bb)?(r=i>0?"bottom":"top",n="center"):(r="middle",n=a>0&&bb>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,textVerticalAlign:r}},Cb=Sb.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},Db=f,Ab=x,kb=Th({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&td(t),kb.superApply(this,"render",arguments),ad(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n){ad(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),kb.superApply(this,"remove",arguments)},dispose:function(t,e){od(this,e),kb.superApply(this,"dispose",arguments)}}),Pb=[];kb.registerAxisPointerClass=function(t,e){Pb[t]=e},kb.getAxisPointerClass=function(t){return t&&Pb[t]};var Lb=["axisLine","axisTickLabel","axisName"],Ob=["splitArea","splitLine","minorSplitLine"],Eb=kb.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new og,this.group.add(this._axisGroup),t.get("show")){var a=t.getCoordSysModel(),o=sd(a,t),s=new Sb(t,o);f(Lb,s.add,s),this._axisGroup.add(s.getGroup()),f(Ob,function(e){t.get(e+".show")&&this["_"+e](t,a)},this),io(r,this._axisGroup,t),Eb.superCall(this,"render",t,e,i,n)}},remove:function(){this._splitAreaColors=null},_splitLine:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),r=n.getModel("lineStyle"),a=r.get("color");a=_(a)?a:[a];for(var o=e.coordinateSystem.getRect(),l=i.isHorizontal(),h=0,u=i.getTicksCoords({tickModel:n}),c=[],d=[],f=r.getLineStyle(),p=0;p<u.length;p++){var g=i.toGlobalCoord(u[p].coord);l?(c[0]=g,c[1]=o.y,d[0]=g,d[1]=o.y+o.height):(c[0]=o.x,c[1]=g,d[0]=o.x+o.width,d[1]=g);var v=h++%a.length,m=u[p].tickValue;this._axisGroup.add(new zm({anid:null!=m?"line_"+u[p].tickValue:null,subPixelOptimize:!0,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:a[v]},f),silent:!0}))}}},_minorSplitLine:function(t,e){var i=t.axis,n=t.getModel("minorSplitLine"),r=n.getModel("lineStyle"),a=e.coordinateSystem.getRect(),o=i.isHorizontal(),s=i.getMinorTicksCoords();if(s.length)for(var l=[],h=[],u=r.getLineStyle(),c=0;c<s.length;c++)for(var d=0;d<s[c].length;d++){var f=i.toGlobalCoord(s[c][d].coord);o?(l[0]=f,l[1]=a.y,h[0]=f,h[1]=a.y+a.height):(l[0]=a.x,l[1]=f,h[0]=a.x+a.width,h[1]=f),this._axisGroup.add(new zm({anid:"minor_line_"+s[c][d].tickValue,subPixelOptimize:!0,shape:{x1:l[0],y1:l[1],x2:h[0],y2:h[1]},style:u,silent:!0}))}},_splitArea:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitArea"),r=n.getModel("areaStyle"),a=r.get("color"),o=e.coordinateSystem.getRect(),l=i.getTicksCoords({tickModel:n,clamp:!0});if(l.length){var h=a.length,u=this._splitAreaColors,c=N(),d=0;if(u)for(var f=0;f<l.length;f++){var p=u.get(l[f].tickValue);if(null!=p){d=(p+(h-1)*f)%h;break}}var g=i.toGlobalCoord(l[0].coord),v=r.getAreaStyle();a=_(a)?a:[a];for(var f=1;f<l.length;f++){var m,y,x,w,b=i.toGlobalCoord(l[f].coord);i.isHorizontal()?(m=g,y=o.y,x=b-m,w=o.height,g=m+x):(m=o.x,y=g,x=o.width,w=b-y,g=y+w);var S=l[f-1].tickValue;null!=S&&c.set(S,d),this._axisGroup.add(new Om({anid:null!=S?"area_"+S:null,shape:{x:m,y:y,width:x,height:w},style:s({fill:a[d]},v),silent:!0})),d=(d+1)%h}this._splitAreaColors=c}}}});Eb.extend({type:"xAxis"}),Eb.extend({type:"yAxis"}),Th({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new Om({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))
}}),gh(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),wh(w_.VISUAL.LAYOUT,x(Tu,"bar")),wh(w_.VISUAL.PROGRESSIVE_LAYOUT,pw),bh({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}}),vx.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(){return lu(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var zb=ld.prototype,Bb=ld.getSymbolSize=function(t,e){var i=t.getItemVisual(e,"symbolSize");return i instanceof Array?i.slice():[+i,+i]};zb._createSymbol=function(t,e,i,n,r){this.removeAll();var a=e.getItemVisual(i,"color"),o=Gu(t,-1,-1,2,2,a,r);o.attr({z2:100,culling:!0,scale:hd(n)}),o.drift=ud,this._symbolType=t,this.add(o)},zb.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},zb.getSymbolPath=function(){return this.childAt(0)},zb.getScale=function(){return this.childAt(0).scale},zb.highlight=function(){this.childAt(0).trigger("emphasis")},zb.downplay=function(){this.childAt(0).trigger("normal")},zb.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},zb.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},zb.updateData=function(t,e,i){this.silent=!1;var n=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,a=Bb(t,e),o=n!==this._symbolType;if(o){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(n,t,e,a,s)}else{var l=this.childAt(0);l.silent=!1,$a(l,{scale:hd(a)},r,e)}if(this._updateCommon(t,e,a,i),o){var l=this.childAt(0),h=i&&i.fadeIn,u={scale:l.scale.slice()};h&&(u.style={opacity:l.style.opacity}),l.scale=[0,0],h&&(l.style.opacity=0),Qa(l,u,r,e)}this._seriesModel=r};var Rb=["itemStyle"],Nb=["emphasis","itemStyle"],Fb=["label"],Vb=["emphasis","label"];zb._updateCommon=function(t,e,i,n){function r(e){return b?t.getName(e):Mc(t,e)}var a=this.childAt(0),s=t.hostModel,l=t.getItemVisual(e,"color");"image"!==a.type?a.useStyle({strokeNoScale:!0}):a.setStyle({opacity:null,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var h=n&&n.itemStyle,u=n&&n.hoverItemStyle,c=n&&n.symbolRotate,d=n&&n.symbolOffset,f=n&&n.labelModel,p=n&&n.hoverLabelModel,g=n&&n.hoverAnimation,v=n&&n.cursorStyle;if(!n||t.hasItemOption){var m=n&&n.itemModel?n.itemModel:t.getItemModel(e);h=m.getModel(Rb).getItemStyle(["color"]),u=m.getModel(Nb).getItemStyle(),c=m.getShallow("symbolRotate"),d=m.getShallow("symbolOffset"),f=m.getModel(Fb),p=m.getModel(Vb),g=m.getShallow("hoverAnimation"),v=m.getShallow("cursor")}else u=o({},u);var y=a.style;a.attr("rotation",(c||0)*Math.PI/180||0),d&&a.attr("position",[xo(d[0],i[0]),xo(d[1],i[1])]),v&&a.attr("cursor",v),a.setColor(l,n&&n.symbolInnerColor),a.setStyle(h);var x=t.getItemVisual(e,"opacity");null!=x&&(y.opacity=x);var _=t.getItemVisual(e,"liftZ"),w=a.__z2Origin;null!=_?null==w&&(a.__z2Origin=a.z2,a.z2+=_):null!=w&&(a.z2=w,a.__z2Origin=null);var b=n&&n.useNameLabel;Fa(y,u,f,p,{labelFetcher:s,labelDataIndex:e,defaultText:r,isRectText:!0,autoColor:l}),a.__symbolOriginalScale=hd(i),a.hoverStyle=u,a.highDownOnUpdate=g&&s.isAnimationEnabled()?cd:null,za(a)},zb.fadeOut=function(t,e){var i=this.childAt(0);this.silent=i.silent=!0,!(e&&e.keepLabel)&&(i.style.text=null),$a(i,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},u(ld,og);var Hb=dd.prototype;Hb.updateData=function(t,e){e=pd(e);var i=this.group,n=t.hostModel,r=this._data,a=this._symbolCtor,o=gd(t);r||i.removeAll(),t.diff(r).add(function(n){var r=t.getItemLayout(n);if(fd(t,r,n,e)){var s=new a(t,n,o);s.attr("position",r),t.setItemGraphicEl(n,s),i.add(s)}}).update(function(s,l){var h=r.getItemGraphicEl(l),u=t.getItemLayout(s);return fd(t,u,s,e)?(h?(h.updateData(t,s,o),$a(h,{position:u},n)):(h=new a(t,s),h.attr("position",u)),i.add(h),void t.setItemGraphicEl(s,h)):void i.remove(h)}).remove(function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut(function(){i.remove(e)})}).execute(),this._data=t},Hb.isPersistent=function(){return!0},Hb.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,i){var n=t.getItemLayout(i);e.attr("position",n)})},Hb.incrementalPrepareUpdate=function(t){this._seriesScope=gd(t),this._data=null,this.group.removeAll()},Hb.incrementalUpdate=function(t,e,i){function n(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}i=pd(i);for(var r=t.start;r<t.end;r++){var a=e.getItemLayout(r);if(fd(e,a,r,i)){var o=new this._symbolCtor(e,r,this._seriesScope);o.traverse(n),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o)}}},Hb.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var Wb=function(t,e,i,n,r,a,o,s){for(var l=xd(t,e),h=[],u=[],c=[],d=[],f=[],p=[],g=[],v=vd(r,e,o),m=vd(a,t,s),y=0;y<l.length;y++){var x=l[y],_=!0;switch(x.cmd){case"=":var w=t.getItemLayout(x.idx),b=e.getItemLayout(x.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),h.push(w),u.push(b),c.push(i[x.idx]),d.push(n[x.idx1]),g.push(e.getRawIndex(x.idx1));break;case"+":var S=x.idx;h.push(r.dataToPoint([e.get(v.dataDimsForPoint[0],S),e.get(v.dataDimsForPoint[1],S)])),u.push(e.getItemLayout(S).slice()),c.push(yd(v,r,e,S)),d.push(n[S]),g.push(e.getRawIndex(S));break;case"-":var S=x.idx,M=t.getRawIndex(S);M!==S?(h.push(t.getItemLayout(S)),u.push(a.dataToPoint([t.get(m.dataDimsForPoint[0],S),t.get(m.dataDimsForPoint[1],S)])),c.push(i[S]),d.push(yd(m,a,t,S)),g.push(M)):_=!1}_&&(f.push(x),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var I=[],T=[],C=[],D=[],A=[],y=0;y<p.length;y++){var S=p[y];I[y]=h[S],T[y]=u[S],C[y]=c[S],D[y]=d[S],A[y]=f[S]}return{current:I,next:T,stackedOnCurrent:C,stackedOnNext:D,status:A}},Gb=oe,Xb=se,Yb=U,Ub=W,jb=[],qb=[],Zb=[],Kb=Qr.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:Mm(Qr.prototype.brush),buildPath:function(t,e){var i=e.points,n=0,r=i.length,a=Md(i,e.smoothConstraint);if(e.connectNulls){for(;r>0&&_d(i[r-1]);r--);for(;r>n&&_d(i[n]);n++);}for(;r>n;)n+=wd(t,i,n,r,r,1,a.min,a.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),$b=Qr.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:Mm(Qr.prototype.brush),buildPath:function(t,e){var i=e.points,n=e.stackedOnPoints,r=0,a=i.length,o=e.smoothMonotone,s=Md(i,e.smoothConstraint),l=Md(n,e.smoothConstraint);if(e.connectNulls){for(;a>0&&_d(i[a-1]);a--);for(;a>r&&_d(i[r]);r++);}for(;a>r;){var h=wd(t,i,r,a,a,1,s.min,s.max,e.smooth,o,e.connectNulls);wd(t,n,r+h-1,h,a,-1,l.min,l.max,e.stackedOnSmooth,o,e.connectNulls),r+=h+1,t.closePath()}}});sl.extend({type:"line",init:function(){var t=new og,e=new dd;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,i){var n=t.coordinateSystem,r=this.group,a=t.getData(),o=t.getModel("lineStyle"),l=t.getModel("areaStyle"),h=a.mapArray(a.getItemLayout),u="polar"===n.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,v=t.get("animation"),m=!l.isEmpty(),y=l.get("origin"),x=vd(n,a,y),_=Cd(n,a,x),w=t.get("showSymbol"),b=w&&!u&&kd(t,a,n),S=this._data;S&&S.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),S.setItemGraphicEl(e,null))}),w||d.remove(),r.add(g);var M,I=!u&&t.get("step");n&&n.getArea&&t.get("clip",!0)&&(M=n.getArea(),null!=M.width?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r1+=.5)),this._clipShapeForSymbol=M,f&&c.type===n.type&&I===this._step?(m&&!p?p=this._newPolygon(h,_,n,v):p&&!m&&(g.remove(p),p=this._polygon=null),g.setClipPath(Ld(n,!1,t)),w&&d.updateData(a,{isIgnore:b,clipShape:M}),a.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),Id(this._stackedOnPoints,_)&&Id(this._points,h)||(v?this._updateAnimation(a,_,n,i,I,y):(I&&(h=Dd(h,n,I),_=Dd(_,n,I)),f.setShape({points:h}),p&&p.setShape({points:h,stackedOnPoints:_})))):(w&&d.updateData(a,{isIgnore:b,clipShape:M}),I&&(h=Dd(h,n,I),_=Dd(_,n,I)),f=this._newPolyline(h,n,v),m&&(p=this._newPolygon(h,_,n,v)),g.setClipPath(Ld(n,!0,t)));var T=Ad(a,n)||a.getVisual("color");f.useStyle(s(o.getLineStyle(),{fill:"none",stroke:T,lineJoin:"bevel"}));var C=t.get("smooth");if(C=Td(t.get("smooth")),f.setShape({smooth:C,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var D=a.getCalculationInfo("stackedOnSeries"),A=0;p.useStyle(s(l.getAreaStyle(),{fill:T,opacity:.7,lineJoin:"bevel"})),D&&(A=Td(D.get("smooth"))),p.setShape({smooth:C,stackedOnSmooth:A,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=a,this._coordSys=n,this._stackedOnPoints=_,this._points=h,this._step=I,this._valueOrigin=y},dispose:function(){},highlight:function(t,e,i,n){var r=t.getData(),a=ar(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;o=new ld(r,a),o.position=s,o.setZ(t.get("zlevel"),t.get("z")),o.ignore=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else sl.prototype.highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t.getData(),a=ar(r,n);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else sl.prototype.downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new Kb({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new $b({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_updateAnimation:function(t,e,i,n,r,a){var o=this._polyline,s=this._polygon,l=t.hostModel,h=Wb(this._data,t,this._stackedOnPoints,e,this._coordSys,i,this._valueOrigin,a),u=h.current,c=h.stackedOnCurrent,d=h.next,f=h.stackedOnNext;r&&(u=Dd(h.current,i,r),c=Dd(h.stackedOnCurrent,i,r),d=Dd(h.next,i,r),f=Dd(h.stackedOnNext,i,r)),o.shape.__points=h.current,o.shape.points=u,$a(o,{shape:{points:d}},l),s&&(s.setShape({points:u,stackedOnPoints:c}),$a(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=h.status,v=0;v<g.length;v++){var m=g[v].cmd;if("="===m){var y=t.getItemGraphicEl(g[v].idx1);y&&p.push({el:y,ptIdx:v})}}o.animators&&o.animators.length&&o.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",o.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),e&&e.eachItemGraphicEl(function(i,n){i.__temp&&(t.remove(i),e.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});var Qb=function(t,e,i){return{seriesType:t,performRawSeries:!0,reset:function(t,n){function r(e,i){if(c){var n=t.getRawValue(i),r=t.getDataParams(i);h&&e.setItemVisual(i,"symbol",o(n,r)),u&&e.setItemVisual(i,"symbolSize",s(n,r))}if(e.hasItemOption){var a=e.getItemModel(i),l=a.getShallow("symbol",!0),d=a.getShallow("symbolSize",!0),f=a.getShallow("symbolKeepAspect",!0);null!=l&&e.setItemVisual(i,"symbol",l),null!=d&&e.setItemVisual(i,"symbolSize",d),null!=f&&e.setItemVisual(i,"symbolKeepAspect",f)}}var a=t.getData(),o=t.get("symbol"),s=t.get("symbolSize"),l=t.get("symbolKeepAspect"),h=w(o),u=w(s),c=h||u,d=!h&&o?o:e,f=u?null:s;return a.setVisual({legendSymbol:i||d,symbol:d,symbolSize:f,symbolKeepAspect:l}),n.isSeriesFiltered(t)?void 0:{dataEach:a.hasItemOption||c?r:null}}}},Jb=function(t){return{seriesType:t,plan:xx(),reset:function(t){function e(t,e){for(var i=t.end-t.start,r=a&&new Float32Array(i*s),l=t.start,h=0,u=[],c=[];l<t.end;l++){var d;if(1===s){var f=e.get(o[0],l);d=!isNaN(f)&&n.dataToPoint(f,null,c)}else{var f=u[0]=e.get(o[0],l),p=u[1]=e.get(o[1],l);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(u,null,c)}a?(r[h++]=d?d[0]:0/0,r[h++]=d?d[1]:0/0):e.setItemLayout(l,d&&d.slice()||[0/0,0/0])}a&&e.setLayout("symbolPoints",r)}var i=t.getData(),n=t.coordinateSystem,r=t.pipelineContext,a=r.large;if(n){var o=p(n.dimensions,function(t){return i.mapDimension(t)}).slice(0,2),s=o.length,l=i.getCalculationInfo("stackResultDimension");return ou(i,o[0])&&(o[0]=l),ou(i,o[1])&&(o[1]=l),s&&{progress:e}}}}},tS={average:function(t){for(var e=0,i=0,n=0;n<t.length;n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t.length;i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t.length;i++)t[i]>e&&(e=t[i]);return isFinite(e)?e:0/0},min:function(t){for(var e=1/0,i=0;i<t.length;i++)t[i]<e&&(e=t[i]);return isFinite(e)?e:0/0},nearest:function(t){return t[0]}},eS=function(t){return Math.round(t.length/2)},iS=function(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t){var e=t.getData(),i=t.get("sampling"),n=t.coordinateSystem;if("cartesian2d"===n.type&&i){var r=n.getBaseAxis(),a=n.getOtherAxis(r),o=r.getExtent(),s=o[1]-o[0],l=Math.round(e.count()/s);if(l>1){var h;"string"==typeof i?h=tS[i]:"function"==typeof i&&(h=i),h&&t.setData(e.downSample(e.mapDimension(a.dim),1/l,h,eS))}}}}};bh(Qb("line","circle","line")),wh(Jb("line")),vh(w_.PROCESSOR.STATISTIC,iS("line"));var nS=function(t,e,i){e=_(e)&&{coordDimensions:e}||o({},e);var n=t.getSource(),r=nw(n,e),a=new tw(r,t);return a.initData(n,i),a},rS={updateSelectedMap:function(t){this._targetList=_(t)?t.slice():[],this._selectTargetMap=g(t||[],function(t,e){return t.set(e.name,e),t},N())},select:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t),n=this.get("selectedMode");"single"===n&&this._selectTargetMap.each(function(t){t.selected=!1}),i&&(i.selected=!0)},unSelect:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);i&&(i.selected=!1)},toggleSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return null!=i?(this[i.selected?"unSelect":"select"](t,e),i.selected):void 0},isSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return i&&i.selected}},aS=Ch({type:"series.pie",init:function(t){aS.superApply(this,"init",arguments),this.legendVisualProvider=new Od(y(this.getData,this),y(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){aS.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(){return nS(this,{coordDimensions:["value"],encodeDefaulter:x(hs,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),i=[],n=0,r=t.count();r>n;n++)i.push({name:t.getName(n),value:t.get(e,n),selected:qs(t,n,"selected")});return i},getDataParams:function(t){var e=this.getData(),i=aS.superCall(this,"getDataParams",t),n=[];return e.each(e.mapDimension("value"),function(t){n.push(t)}),i.percent=Io(n,t,e.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(t){Qn(t,"labelLine",["show"]);var e=t.labelLine,i=t.emphasis.labelLine;e.show=e.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});c(aS,rS);var oS=Bd.prototype;oS.updateData=function(t,e,i){var n=this.childAt(0),r=this.childAt(1),a=this.childAt(2),l=t.hostModel,h=t.getItemModel(e),u=t.getItemLayout(e),c=o({},u);c.label=null;var d=l.getShallow("animationTypeUpdate");if(i){n.setShape(c);var f=l.getShallow("animationType");"scale"===f?(n.shape.r=u.r0,Qa(n,{shape:{r:u.r}},l,e)):(n.shape.endAngle=u.startAngle,$a(n,{shape:{endAngle:u.endAngle}},l,e))}else"expansion"===d?n.setShape(c):$a(n,{shape:c},l,e);var p=t.getItemVisual(e,"color");n.useStyle(s({lineJoin:"bevel",fill:p},h.getModel("itemStyle").getItemStyle())),n.hoverStyle=h.getModel("emphasis.itemStyle").getItemStyle();var g=h.getShallow("cursor");g&&n.attr("cursor",g),zd(this,t.getItemLayout(e),l.isSelected(null,e),l.get("selectedOffset"),l.get("animation"));var v=!i&&"transition"===d;this._updateLabel(t,e,v),this.highDownOnUpdate=h.get("hoverAnimation")&&l.isAnimationEnabled()?function(t,e){"emphasis"===e?(r.ignore=r.hoverIgnore,a.ignore=a.hoverIgnore,n.stopAnimation(!0),n.animateTo({shape:{r:u.r+l.get("hoverOffset")}},300,"elasticOut")):(r.ignore=r.normalIgnore,a.ignore=a.normalIgnore,n.stopAnimation(!0),n.animateTo({shape:{r:u.r}},300,"elasticOut"))}:null,za(this)},oS._updateLabel=function(t,e,i){var n=this.childAt(1),r=this.childAt(2),a=t.hostModel,o=t.getItemModel(e),s=t.getItemLayout(e),l=s.label,h=t.getItemVisual(e,"color");if(!l||isNaN(l.x)||isNaN(l.y))return void(r.ignore=r.normalIgnore=r.hoverIgnore=n.ignore=n.normalIgnore=n.hoverIgnore=!0);var u={points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]},c={x:l.x,y:l.y};i?($a(n,{shape:u},a,e),$a(r,{style:c},a,e)):(n.attr({shape:u}),r.attr({style:c})),r.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var d=o.getModel("label"),f=o.getModel("emphasis.label"),p=o.getModel("labelLine"),g=o.getModel("emphasis.labelLine"),h=t.getItemVisual(e,"color");Fa(r.style,r.hoverStyle={},d,f,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:l.text,autoColor:h,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),r.ignore=r.normalIgnore=!d.get("show"),r.hoverIgnore=!f.get("show"),n.ignore=n.normalIgnore=!p.get("show"),n.hoverIgnore=!g.get("show"),n.setStyle({stroke:h,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(p.getModel("lineStyle").getLineStyle()),n.hoverStyle=g.getModel("lineStyle").getLineStyle();var v=p.get("smooth");v&&v===!0&&(v=.4),n.setShape({smooth:v})},u(Bd,og);var sS=(sl.extend({type:"pie",init:function(){var t=new og;this._sectorGroup=t},render:function(t,e,i,n){if(!n||n.from!==this.uid){var r=t.getData(),a=this._data,o=this.group,s=e.get("animation"),l=!a,h=t.get("animationType"),u=t.get("animationTypeUpdate"),c=x(Ed,this.uid,t,s,i),d=t.get("selectedMode");if(r.diff(a).add(function(t){var e=new Bd(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),o.add(e)}).update(function(t,e){var i=a.getItemGraphicEl(e);l||"transition"===u||i.eachChild(function(t){t.stopAnimation(!0)}),i.updateData(r,t),i.off("click"),d&&i.on("click",c),o.add(i),r.setItemGraphicEl(t,i)}).remove(function(t){var e=a.getItemGraphicEl(t);o.remove(e)}).execute(),s&&r.count()>0&&(l?"scale"!==h:"transition"!==u)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(i.getWidth(),i.getHeight())/2,v=y(o.removeClipPath,o);o.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,v,t,l))}else o.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,i,n,r,a,o,s){var l=new Im({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}}),h=s?Qa:$a;return h(l,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},o,a),l},containPoint:function(t,e){var i=e.getData(),n=i.getItemLayout(0);if(n){var r=t[0]-n.cx,a=t[1]-n.cy,o=Math.sqrt(r*r+a*a);return o<=n.r&&o>=n.r0}}}),function(t,e){f(e,function(e){e.update="updateView",yh(e,function(i,n){var r={};return n.eachComponent({mainType:"series",subType:t,query:i},function(t){t[e.method]&&t[e.method](i.name,i.dataIndex);var n=t.getData();n.each(function(e){var i=n.getName(e);r[i]=t.isSelected(i)||!1})}),{name:i.name,selected:r,seriesId:i.seriesId}})})}),lS=function(t){return{getTargetSeries:function(e){var i={},n=N();return e.eachSeriesByType(t,function(t){t.__paletteScope=i,n.set(t.uid,t)}),n},reset:function(t){var e=t.getRawData(),i={},n=t.getData();n.each(function(t){var e=n.getRawIndex(t);i[e]=t}),e.each(function(r){var a,o=i[r],s=null!=o&&n.getItemVisual(o,"color",!0),l=null!=o&&n.getItemVisual(o,"borderColor",!0);if(s&&l||(a=e.getItemModel(r)),!s){var h=a.get("itemStyle.color")||t.getColorFromPalette(e.getName(r)||r+"",t.__paletteScope,e.count());null!=o&&n.setItemVisual(o,"color",h)}if(!l){var u=a.get("itemStyle.borderColor");null!=o&&n.setItemVisual(o,"borderColor",u)}})}}},hS=Math.PI/180,uS=function(t,e,i,n,r,a){var o,s,l=t.getData(),h=[],u=!1,c=(t.get("minShowLabelAngle")||0)*hS;l.each(function(n){var a=l.getItemLayout(n),d=l.getItemModel(n),f=d.getModel("label"),p=f.get("position")||d.get("emphasis.label.position"),g=f.get("distanceToLabelLine"),v=f.get("alignTo"),m=xo(f.get("margin"),i),y=f.get("bleedMargin"),x=f.getFont(),_=d.getModel("labelLine"),w=_.get("length");w=xo(w,i);var b=_.get("length2");if(b=xo(b,i),!(a.angle<c)){var S,M,I,T,C=(a.startAngle+a.endAngle)/2,D=Math.cos(C),A=Math.sin(C);o=a.cx,s=a.cy;var k=t.getFormattedLabel(n,"normal")||l.getName(n),P=Wi(k,x,T,"top"),L="inside"===p||"inner"===p;if("center"===p)S=a.cx,M=a.cy,T="center";else{var O=(L?(a.r+a.r0)/2*D:a.r*D)+o,E=(L?(a.r+a.r0)/2*A:a.r*A)+s;if(S=O+3*D,M=E+3*A,!L){var z=O+D*(w+e-a.r),B=E+A*(w+e-a.r),R=z+(0>D?-1:1)*b,N=B;S="edge"===v?0>D?r+m:r+i-m:R+(0>D?-g:g),M=N,I=[[O,E],[z,B],[R,N]]}T=L?"center":"edge"===v?D>0?"right":"left":D>0?"left":"right"}var F,V=f.get("rotate");F="number"==typeof V?V*(Math.PI/180):V?0>D?-C+Math.PI:-C:0,u=!!F,a.label={x:S,y:M,position:p,height:P.height,len:w,len2:b,linePoints:I,textAlign:T,verticalAlign:"middle",rotation:F,inside:L,labelDistance:g,labelAlignTo:v,labelMargin:m,bleedMargin:y,textRect:P,text:k,font:x},L||h.push(a.label)}}),!u&&t.get("avoidLabelOverlap")&&Nd(h,o,s,e,i,n,r,a)},cS=2*Math.PI,dS=Math.PI/180,fS=function(t,e,i){e.eachSeriesByType(t,function(t){var e=t.getData(),n=e.mapDimension("value"),r=Vd(t,i),a=t.get("center"),o=t.get("radius");_(o)||(o=[0,o]),_(a)||(a=[a,a]);var s=xo(r.width,i.getWidth()),l=xo(r.height,i.getHeight()),h=Math.min(s,l),u=xo(a[0],s)+r.x,c=xo(a[1],l)+r.y,d=xo(o[0],h/2),f=xo(o[1],h/2),p=-t.get("startAngle")*dS,g=t.get("minAngle")*dS,v=0;e.each(n,function(t){!isNaN(t)&&v++});var m=e.getSum(n),y=Math.PI/(m||v)*2,x=t.get("clockwise"),w=t.get("roseType"),b=t.get("stillShowZeroSum"),S=e.getDataExtent(n);S[0]=0;var M=cS,I=0,T=p,C=x?1:-1;if(e.each(n,function(t,i){var n;if(isNaN(t))return void e.setItemLayout(i,{angle:0/0,startAngle:0/0,endAngle:0/0,clockwise:x,cx:u,cy:c,r0:d,r:w?0/0:f,viewRect:r});n="area"!==w?0===m&&b?y:t*y:cS/v,g>n?(n=g,M-=g):I+=t;var a=T+C*n;e.setItemLayout(i,{angle:n,startAngle:T,endAngle:a,clockwise:x,cx:u,cy:c,r0:d,r:w?yo(t,S,[d,f]):f,viewRect:r}),T=a}),cS>M&&v)if(.001>=M){var D=cS/v;e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i);n.angle=D,n.startAngle=p+C*i*D,n.endAngle=p+C*(i+1)*D}})}else y=M/I,T=p,e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i),r=n.angle===g?g:t*y;n.startAngle=T,n.endAngle=T+C*r,T+=C*r}});uS(t,f,r.width,r.height,r.x,r.y)})},pS=function(t){return{seriesType:t,reset:function(t,e){var i=e.findComponents({mainType:"legend"});if(i&&i.length){var n=t.getData();n.filterSelf(function(t){for(var e=n.getName(t),r=0;r<i.length;r++)if(!i[r].isSelected(e))return!1;return!0})}}}};sS("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),bh(lS("pie")),wh(x(fS,"pie")),vh(pS("pie")),Ih({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Th({type:"title",render:function(t,e,i){if(this.group.removeAll(),t.get("show")){var n=this.group,r=t.getModel("textStyle"),a=t.getModel("subtextStyle"),o=t.get("textAlign"),s=A(t.get("textBaseline"),t.get("textVerticalAlign")),l=new wm({style:Ha({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),h=l.getBoundingRect(),u=t.get("subtext"),c=new wm({style:Ha({},a,{text:u,textFill:a.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){window.open(d,"_"+t.get("target"))}),f&&c.on("click",function(){window.open(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,n.add(l),u&&n.add(c);var g=n.getBoundingRect(),v=t.getBoxLayoutParams();v.width=g.width,v.height=g.height;var m=jo(v,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));o||(o=t.get("left")||t.get("right"),"middle"===o&&(o="center"),"right"===o?m.x+=m.width:"center"===o&&(m.x+=m.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),n.attr("position",[m.x,m.y]);var y={textAlign:o,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=n.getBoundingRect();var x=m.margin,_=t.getItemStyle(["color","opacity"]);_.fill=t.get("backgroundColor");var w=new Om({shape:{x:g.x-x[3],y:g.y-x[0],width:g.width+x[1]+x[3],height:g.height+x[0]+x[2],r:t.get("borderRadius")},style:_,subPixelOptimize:!0,silent:!0});n.add(w)}}});var gS=Ax.legend.selector,vS={all:{type:"all",title:n(gS.all)},inverse:{type:"inverse",title:n(gS.inverse)}},mS=Ih({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){mS.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var e=t.selector;e===!0&&(e=t.selector=["all","inverse"]),_(e)&&f(e,function(t,i){b(t)&&(t={type:t}),e[i]=r(t,vS[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],i=[];t.eachRawSeries(function(n){var r=n.name;i.push(r);var a;if(n.legendVisualProvider){var o=n.legendVisualProvider,s=o.getAllNames();t.isSeriesFiltered(n)||(i=i.concat(s)),s.length?e=e.concat(s):a=!0}else a=!0;a&&nr(n)&&e.push(n.name)}),this._availableNames=i;var n=this.get("data")||e,r=p(n,function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new uo(t,this,this.ecModel)},this);this._data=r},getData:function(){return this._data},select:function(t){var e=this.option.selected,i=this.get("selectedMode");if("single"===i){var n=this._data;f(n,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){var i=t.get("name",!0);e.hasOwnProperty(i)||(e[i]=!0),e[i]=!e[i]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&h(this._availableNames,t)>=0},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});yh("legendToggleSelect","legendselectchanged",x(Hd,"toggleSelected")),yh("legendAllSelect","legendselectall",x(Hd,"allSelect")),yh("legendInverseSelect","legendinverseselect",x(Hd,"inverseSelect")),yh("legendSelect","legendselected",x(Hd,"select")),yh("legendUnSelect","legendunselected",x(Hd,"unSelect"));var yS=x,xS=f,_S=og,wS=Th({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new _S),this._backgroundEl,this.group.add(this._selectorGroup=new _S),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),a=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===a?"right":"left");var o=t.get("selector",!0),l=t.get("selectorPosition",!0);!o||l&&"auto"!==l||(l="horizontal"===a?"end":"start"),this.renderInner(r,t,e,i,o,a,l);var h=t.getBoxLayoutParams(),u={width:i.getWidth(),height:i.getHeight()},c=t.get("padding"),d=jo(h,u,c),f=this.layoutInner(t,r,d,n,o,l),p=jo(s({width:f.width,height:f.height},h),u,c);this.group.attr("position",[p.x-f.x,p.y-f.y]),this.group.add(this._backgroundEl=Wd(f,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(t,e,i,n,r,a,o){var s=this.getContentGroup(),l=N(),h=e.get("selectedMode"),u=[];i.eachRawSeries(function(t){!t.get("legendHoverLink")&&u.push(t.id)}),xS(e.getData(),function(r,a){var o=r.get("name");if(!this.newlineDisabled&&(""===o||"\n"===o))return void s.add(new _S({newline:!0}));var c=i.getSeriesByName(o)[0];if(!l.get(o))if(c){var d=c.getData(),f=d.getVisual("color"),p=d.getVisual("borderColor");"function"==typeof f&&(f=f(c.getDataParams(0))),"function"==typeof p&&(p=p(c.getDataParams(0)));var g=d.getVisual("legendSymbol")||"roundRect",v=d.getVisual("symbol"),m=this._createItem(o,a,r,e,g,v,t,f,p,h);m.on("click",yS(Xd,o,null,n,u)).on("mouseover",yS(Yd,c.name,null,n,u)).on("mouseout",yS(Ud,c.name,null,n,u)),l.set(o,!0)}else i.eachRawSeries(function(i){if(!l.get(o)&&i.legendVisualProvider){var s=i.legendVisualProvider;if(!s.containName(o))return;var c=s.indexOfName(o),d=s.getItemVisual(c,"color"),f=s.getItemVisual(c,"borderColor"),p="roundRect",g=this._createItem(o,a,r,e,p,null,t,d,f,h);g.on("click",yS(Xd,null,o,n,u)).on("mouseover",yS(Yd,null,o,n,u)).on("mouseout",yS(Ud,null,o,n,u)),l.set(o,!0)}},this)},this),r&&this._createSelector(r,e,n,a,o)},_createSelector:function(t,e,i){function n(t){var n=t.type,a=new wm({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){i.dispatchAction({type:"all"===n?"legendAllSelect":"legendInverseSelect"})}});r.add(a);var o=e.getModel("selectorLabel"),s=e.getModel("emphasis.selectorLabel");Fa(a.style,a.hoverStyle={},o,s,{defaultText:t.title,isRectText:!1}),za(a)}var r=this.getSelectorGroup();xS(t,function(t){n(t)})},_createItem:function(t,e,i,n,r,a,s,l,h,u){var c=n.get("itemWidth"),d=n.get("itemHeight"),f=n.get("inactiveColor"),p=n.get("inactiveBorderColor"),g=n.get("symbolKeepAspect"),v=n.getModel("itemStyle"),m=n.isSelected(t),y=new _S,x=i.getModel("textStyle"),_=i.get("icon"),w=i.getModel("tooltip"),b=w.parentModel;
r=_||r;var S=Gu(r,0,0,c,d,m?l:f,null==g?!0:g);if(y.add(Gd(S,r,v,h,p,m)),!_&&a&&(a!==r||"none"===a)){var M=.8*d;"none"===a&&(a="circle");var I=Gu(a,(c-M)/2,(d-M)/2,M,M,m?l:f,null==g?!0:g);y.add(Gd(I,a,v,h,p,m))}var T="left"===s?c+5:-5,C=s,D=n.get("formatter"),A=t;"string"==typeof D&&D?A=D.replace("{name}",null!=t?t:""):"function"==typeof D&&(A=D(t)),y.add(new wm({style:Ha({},x,{text:A,x:T,y:d/2,textFill:m?x.getTextColor():f,textAlign:C,textVerticalAlign:"middle"})}));var k=new Om({shape:y.getBoundingRect(),invisible:!0,tooltip:w.get("show")?o({content:t,formatter:b.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:t,$vars:["name"]}},w.option):null});return y.add(k),y.eachChild(function(t){t.silent=!0}),k.silent=!u,this.getContentGroup().add(y),za(y),y.__legendDataIndex=e,y},layoutInner:function(t,e,i,n,r,a){var o=this.getContentGroup(),s=this.getSelectorGroup();Ty(t.get("orient"),o,t.get("itemGap"),i.width,i.height);var l=o.getBoundingRect(),h=[-l.x,-l.y];if(r){Ty("horizontal",s,t.get("selectorItemGap",!0));var u=s.getBoundingRect(),c=[-u.x,-u.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",v=0===f?"y":"x";"end"===a?c[f]+=l[p]+d:h[f]+=u[p]+d,c[1-f]+=l[g]/2-u[g]/2,s.attr("position",c),o.attr("position",h);var m={x:0,y:0};return m[p]=l[p]+d+u[p],m[g]=Math.max(l[g],u[g]),m[v]=Math.min(0,u[v]+c[1-f]),m}return o.attr("position",h),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}}),bS=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var i=0;i<e.length;i++)if(!e[i].isSelected(t.name))return!1;return!0})};vh(w_.PROCESSOR.SERIES_FILTER,bS),Ay.registerSubTypeDefaulter("legend",function(){return"plain"});var SS=mS.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,i,n){var r=Zo(t);SS.superCall(this,"init",t,e,i,n),jd(this,t,r)},mergeOption:function(t,e){SS.superCall(this,"mergeOption",t,e),jd(this,this.option,t)}}),MS=og,IS=["width","height"],TS=["x","y"],CS=wS.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){CS.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new MS),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new MS),this._showController},resetInner:function(){CS.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,i,n,r,a,o){function s(t,i){var r=t+"DataIndex",a=ao(e.get("pageIcons",!0)[e.getOrient().name][i],{onclick:y(l._pageGo,l,r,e,n)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});a.name=t,h.add(a)}var l=this;CS.superCall(this,"renderInner",t,e,i,n,r,a,o);var h=this._controllerGroup,u=e.get("pageIconSize",!0);_(u)||(u=[u,u]),s("pagePrev",0);var c=e.getModel("pageTextStyle");h.add(new wm({name:"pageText",style:{textFill:c.getTextColor(),font:c.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),s("pageNext",1)},layoutInner:function(t,e,i,r,a,o){var s=this.getSelectorGroup(),l=t.getOrient().index,h=IS[l],u=TS[l],c=IS[1-l],d=TS[1-l];a&&Ty("horizontal",s,t.get("selectorItemGap",!0));var f=t.get("selectorButtonGap",!0),p=s.getBoundingRect(),g=[-p.x,-p.y],v=n(i);a&&(v[h]=i[h]-p[h]-f);var m=this._layoutContentAndController(t,r,v,l,h,c,d);if(a){if("end"===o)g[l]+=m[h]+f;else{var y=p[h]+f;g[l]-=y,m[u]-=y}m[h]+=p[h]+f,g[1-l]+=m[d]+m[c]/2-p[c]/2,m[c]=Math.max(m[c],p[c]),m[d]=Math.min(m[d],p[d]+g[1-l]),s.attr("position",g)}return m},_layoutContentAndController:function(t,e,i,n,r,a,o){var s=this.getContentGroup(),l=this._containerGroup,h=this._controllerGroup;Ty(t.get("orient"),s,t.get("itemGap"),n?i.width:null,n?null:i.height),Ty("horizontal",h,t.get("pageButtonItemGap",!0));var u=s.getBoundingRect(),c=h.getBoundingRect(),d=this._showController=u[r]>i[r],f=[-u.x,-u.y];e||(f[n]=s.position[n]);var p=[0,0],g=[-c.x,-c.y],v=A(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(d){var m=t.get("pageButtonPosition",!0);"end"===m?g[n]+=i[r]-c[r]:p[n]+=c[r]+v}g[1-n]+=u[a]/2-c[a]/2,s.attr("position",f),l.attr("position",p),h.attr("position",g);var y={x:0,y:0};if(y[r]=d?i[r]:u[r],y[a]=Math.max(u[a],c[a]),y[o]=Math.min(0,c[o]+g[1-n]),l.__rectSize=i[r],d){var x={x:0,y:0};x[r]=Math.max(i[r]-c[r]-v,0),x[a]=y[a],l.setClipPath(new Om({shape:x})),l.__rectSize=x[r]}else h.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&$a(s,{position:_.contentPosition},d?t:!1),this._updatePageInfoView(t,_),y},_pageGo:function(t,e,i){var n=this._getPageInfo(e)[t];null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:e.id})},_updatePageInfoView:function(t,e){var i=this._controllerGroup;f(["pagePrev","pageNext"],function(n){var r=null!=e[n+"DataIndex"],a=i.childOfName(n);a&&(a.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),a.cursor=r?"pointer":"default")});var n=i.childOfName("pageText"),r=t.get("pageFormatter"),a=e.pageIndex,o=null!=a?a+1:0,s=e.pageCount;n&&r&&n.setStyle("text",b(r)?r.replace("{current}",o).replace("{total}",s):r({current:o,total:s}))},_getPageInfo:function(t){function e(t){if(t){var e=t.getBoundingRect(),i=e[l]+t.position[o];return{s:i,e:i+e[s],i:t.__legendDataIndex}}}function i(t,e){return t.e>=e&&t.s<=e+a}var n=t.get("scrollDataIndex",!0),r=this.getContentGroup(),a=this._containerGroup.__rectSize,o=t.getOrient().index,s=IS[o],l=TS[o],h=this._findTargetItemIndex(n),u=r.children(),c=u[h],d=u.length,f=d?1:0,p={contentPosition:r.position.slice(),pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return p;var g=e(c);p.contentPosition[o]=-g.s;for(var v=h+1,m=g,y=g,x=null;d>=v;++v)x=e(u[v]),(!x&&y.e>m.s+a||x&&!i(x,m.s))&&(m=y.i>m.i?y:x,m&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=m.i),++p.pageCount)),y=x;for(var v=h-1,m=g,y=g,x=null;v>=-1;--v)x=e(u[v]),x&&i(y,x.s)||!(m.i<y.i)||(y=m,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=m.i),++p.pageCount,++p.pageIndex),m=x;return p},_findTargetItemIndex:function(t){var e,i,n=this.getContentGroup();return this._showController&&n.eachChild(function(n,r){var a=n.__legendDataIndex;null==i&&null!=a&&(i=r),a===t&&(e=r)}),null!=e?e:i}});yh("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;null!=i&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(i)})});var DS=function(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var a=i.getData(),o=ar(a,t);if(null==o||0>o||_(o))return{point:[]};var s=a.getItemGraphicEl(o),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(a.getValues(p(l.dimensions,function(t){return a.mapDimension(t)}),o,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),n=[h.x+h.width/2,h.y+h.height/2]}return{point:n,el:s}},AS=f,kS=x,PS=or(),LS=function(t,e,i){var n=t.currTrigger,r=[t.x,t.y],a=t,o=t.dispatchAction||y(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){rf(r)&&(r=DS({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},e).point);var l=rf(r),h=a.axesInfo,u=s.axesInfo,c="leave"===n||rf(r),d={},f={},p={list:[],map:{}},g={showPointer:kS(Kd,f),showTooltip:kS($d,p)};AS(s.coordSysMap,function(t,e){var i=l||t.containPoint(r);AS(s.coordSysAxesInfo[e],function(t){var e=t.axis,n=ef(h,t);if(!c&&i&&(!h||n)){var a=n&&n.value;null!=a||l||(a=e.pointToData(r)),null!=a&&qd(t,a,g,!1,d)}})});var v={};return AS(u,function(t,e){var i=t.linkGroup;i&&!f[e]&&AS(i.axesInfo,function(e,n){var r=f[n];if(e!==t&&r){var a=r.value;i.mapper&&(a=t.axis.scale.parse(i.mapper(a,nf(e),nf(t)))),v[t.key]=a}})}),AS(v,function(t,e){qd(u[e],t,g,!0,d)}),Qd(f,u,d),Jd(p,r,t,o),tf(u,o,i),d}},OS=(Ih({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),or()),ES=f,zS=Th({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";af("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){cf(e.getZr(),"axisPointer"),zS.superApply(this._model,"remove",arguments)},dispose:function(t,e){cf("axisPointer",e),zS.superApply(this._model,"dispose",arguments)}}),BS=or(),RS=n,NS=y;df.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==a){this._lastValue=r,this._lastStatus=a;var o=this._group,s=this._handle;if(!a||"hide"===a)return o&&o.hide(),void(s&&s.hide());o&&o.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(o){var c=x(ff,e,u);this.updatePointerEl(o,l,c,e),this.updateLabelEl(o,l,c,e)}else o=this._group=new og,this.createPointerEl(o,l,t,e),this.createLabelEl(o,l,t,e),i.getZr().add(o);mf(o,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===i||null==i){var o=this.animationThreshold;if(r&&n.getBandWidth()>o)return!0;if(a){var s=ed(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>o}return!1}return i===!0},makeElOption:function(){},createPointerEl:function(t,e){var i=e.pointer;if(i){var n=BS(t).pointerEl=new ry[i.type](RS(e.pointer));t.add(n)}},createLabelEl:function(t,e,i,n){if(e.label){var r=BS(t).labelEl=new Om(RS(e.label));t.add(r),gf(r,n)}},updatePointerEl:function(t,e,i){var n=BS(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=BS(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),gf(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,r=e.getModel("handle"),a=e.get("status");if(!r.get("show")||!a||"hide"===a)return n&&i.remove(n),void(this._handle=null);var o;this._handle||(o=!0,n=this._handle=ao(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){wp(t.event)},onmousedown:NS(this._onHandleDragMove,this,0,0),drift:NS(this._onHandleDragMove,this),ondragend:NS(this._onHandleDragEnd,this)}),i.add(n)),mf(n,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];n.setStyle(r.getItemStyle(null,s));var l=r.get("size");_(l)||(l=[l,l]),n.attr("scale",[l[0]/2,l[1]/2]),fl(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)}},_moveHandleToValue:function(t,e){ff(this._axisPointerModel,!e&&this._moveAnimation,this._handle,vf(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(vf(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(vf(n)),BS(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}},df.prototype.constructor=df,pr(df);var FS=df.extend({makeElOption:function(t,e,i,n,r){var a=i.axis,o=a.grid,s=n.get("type"),l=Tf(o,a).getOtherAxis(a).getGlobalExtent(),h=a.toGlobalCoord(a.dataToCoord(e,!0));if(s&&"none"!==s){var u=yf(n),c=VS[s](a,h,l);c.style=u,t.graphicKey=c.type,t.pointer=c}var d=sd(o.model,i);Sf(e,t,d,i,n,r)},getHandleTransform:function(t,e,i){var n=sd(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:bf(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i){var n=i.axis,r=n.grid,a=n.getGlobalExtent(!0),o=Tf(r,n).getOtherAxis(n).getGlobalExtent(),s="x"===n.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(a[1],l[s]),l[s]=Math.max(a[0],l[s]);var h=(o[1]+o[0])/2,u=[h,h];u[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:c[s]}}}),VS={line:function(t,e,i){var n=Mf([e,i[0]],[e,i[1]],Cf(t));return{type:"Line",subPixelOptimize:!0,shape:n}},shadow:function(t,e,i){var n=Math.max(1,t.getBandWidth()),r=i[1]-i[0];return{type:"Rect",shape:If([e-n/2,i[0]],[n,r],Cf(t))}}};kb.registerAxisPointerClass("CartesianAxisPointer",FS),gh(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!_(e)&&(t.axisPointer.link=[e])}}),vh(w_.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=qc(t,e)}),yh({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},LS),Ih({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var HS=f,WS=Bo,GS=["","-webkit-","-moz-","-o-"],XS="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";Pf.prototype={constructor:Pf,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i.position&&"absolute"!==e.position&&(i.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=XS+kf(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i,n=this._zr;n&&n.painter&&(i=n.painter.getViewportRootOffset())&&(t+=i.offsetLeft,e+=i.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(t+=parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),e+=parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:t,height:e}}},Lf.prototype={constructor:Lf,_enterable:!0,update:function(){},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,i){this.el&&this._zr.remove(this.el);for(var n={},r=t,a="{marker",o="|}",s=r.indexOf(a);s>=0;){var l=r.indexOf(o),h=r.substr(s+a.length,l-s-a.length);n["marker"+h]=h.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[h],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[h]},r=r.substr(l+1),s=r.indexOf("{marker")}this.el=new wm({style:{rich:n,text:t,textLineHeight:20,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding")},z:i.get("z")}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var YS=y,US=f,jS=xo,qS=new Om({shape:{x:-1,y:-1,width:2,height:2}});Th({type:"tooltip",init:function(t,e){if(!jf.node){var i=t.getComponent("tooltip"),n=i.get("renderMode");this._renderMode=cr(n);var r;"html"===this._renderMode?(r=new Pf(e.getDom(),e),this._newLine="<br/>"):(r=new Lf(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,i){if(!jf.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");af("itemTooltip",this._api,YS(function(t,i,n){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(i,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!i.isDisposed()&&n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!jf.node){var r=Ef(n,i);this._ticket="";var a=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var o=qS;o.position=[n.x,n.y],o.update(),o.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:o},r)}else if(a)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,event:{},dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=DS(n,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:n.position,target:s.el,event:{}},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target,event:{}},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(Ef(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,a=n.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=Of([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:n.position}),!0}}},_tryShow:function(t,e){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=y(e,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var i=this._ecModel,n=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],l=Of([e.tooltipOption,n]),h=this._renderMode,u=this._newLine,c={};US(t,function(t){US(t.dataByAxis,function(t){var e=i.getComponent(t.axisDim+"Axis",t.axisIndex),n=t.value,a=[];if(e&&null!=n){var l=wf(n,e.axis,i,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(o){var u=i.getSeriesByIndex(o.seriesIndex),d=o.dataIndexInside,f=u&&u.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=Ru(e.axis,n),f.axisValueLabel=l,f){s.push(f);var p,g=u.formatTooltip(d,!0,null,h);if(S(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;a.push(p)}});var d=l;o.push("html"!==h?a.join(u):(d?Ro(d)+u:"")+a.join(u))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(l,o,s,Math.random(),a[0],a[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,a=n.getSeriesByIndex(r),o=e.dataModel||a,s=e.dataIndex,l=e.dataType,h=o.getData(),u=Of([h.getItemModel(s),o,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d,f,p=o.getDataParams(s,l),g=o.formatTooltip(s,!1,l,this._renderMode);S(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+o.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),i({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){var r=n;n={content:r,formatter:r}}var a=new uo(n,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var h=this._tooltipContent,u=t.get("formatter");o=o||t.get("position");var c=e;if(u&&"string"==typeof u)c=No(u,i,!0);else if("function"==typeof u){var d=YS(function(e,n){e===this._ticket&&(h.setContent(n,l,t),this._updatePosition(t,o,r,a,h,i,s))},this);this._ticket=n,c=u(i,n,d)}h.setContent(c,l,t),h.show(t),this._updatePosition(t,o,r,a,h,i,s)}},_updatePosition:function(t,e,i,n,r,a,o){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=o&&o.getBoundingRect().clone();if(o&&d.applyTransform(o.transform),"function"==typeof e&&(e=e([i,n],a,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),_(e))i=jS(e[0],s),n=jS(e[1],l);else if(S(e)){e.width=h[0],e.height=h[1];var f=jo(e,{width:s,height:l});i=f.x,n=f.y,u=null,c=null}else if("string"==typeof e&&o){var p=Rf(e,d,h);i=p[0],n=p[1]}else{var p=zf(i,n,r,s,l,u?null:20,c?null:20);i=p[0],n=p[1]}if(u&&(i-=Nf(u)?h[0]/2:"right"===u?h[0]:0),c&&(n-=Nf(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var p=Bf(i,n,r,s,l);i=p[0],n=p[1]}r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,i=!!e&&e.length===t.length;return i&&US(e,function(e,n){var r=e.dataByAxis||{},a=t[n]||{},o=a.dataByAxis||[];i&=r.length===o.length,i&&US(r,function(t,e){var n=o[e]||{},r=t.seriesDataIndices||[],a=n.seriesDataIndices||[];i&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&r.length===a.length,i&&US(r,function(t,e){var n=a[e];i&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=t,!!i},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){jf.node||(this._tooltipContent.hide(),cf("itemTooltip",e))}}),yh({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),yh({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var ZS,KS="urn:schemas-microsoft-com:vml",$S="undefined"==typeof window?null:window,QS=!1,JS=$S&&$S.document;if(JS&&!jf.canvasSupported)try{!JS.namespaces.zrvml&&JS.namespaces.add("zrvml",KS),ZS=function(t){return JS.createElement("<zrvml:"+t+' class="zrvml">')}}catch(tM){ZS=function(t){return JS.createElement("<"+t+' xmlns="'+KS+'" class="zrvml">')}}var eM=Kv.CMD,iM=Math.round,nM=Math.sqrt,rM=Math.abs,aM=Math.cos,oM=Math.sin,sM=Math.max;if(!jf.canvasSupported){var lM=",",hM="progid:DXImageTransform.Microsoft",uM=21600,cM=uM/2,dM=1e5,fM=1e3,pM=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=uM+","+uM,t.coordorigin="0,0"},gM=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},vM=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},mM=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},yM=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},xM=function(t,e,i){return(parseFloat(t)||0)*dM+(parseFloat(e)||0)*fM+i},_M=wn,wM=function(t,e,i){var n=Ke(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=vM(n[0],n[1],n[2]),t.opacity=i*n[3])},bM=function(t){var e=Ke(t);return[vM(e[0],e[1],e[2]),e[3]]},SM=function(t,e,i){var n=e.fill;if(null!=n)if(n instanceof Vm){var r,a=0,o=[0,0],s=0,l=1,h=i.getBoundingRect(),u=h.width,c=h.height;if("linear"===n.type){r="gradient";var d=i.transform,f=[n.x*u,n.y*c],p=[n.x2*u,n.y2*c];d&&(ae(f,f,d),ae(p,p,d));var g=p[0]-f[0],v=p[1]-f[1];a=180*Math.atan2(g,v)/Math.PI,0>a&&(a+=360),1e-6>a&&(a=0)}else{r="gradientradial";var f=[n.x*u,n.y*c],d=i.transform,m=i.scale,y=u,x=c;o=[(f[0]-h.x)/y,(f[1]-h.y)/x],d&&ae(f,f,d),y/=m[0]*uM,x/=m[1]*uM;var _=sM(y,x);s=0/_,l=2*n.r/_-s}var w=n.colorStops.slice();w.sort(function(t,e){return t.offset-e.offset});for(var b=w.length,S=[],M=[],I=0;b>I;I++){var T=w[I],C=bM(T.color);M.push(T.offset*l+s+" "+C[0]),(0===I||I===b-1)&&S.push(C)}if(b>=2){var D=S[0][0],A=S[1][0],k=S[0][1]*e.opacity,P=S[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=a,t.color=D,t.color2=A,t.colors=M.join(","),t.opacity=P,t.opacity2=k}"radial"===r&&(t.focusposition=o.join(","))}else wM(t,n,e.opacity)},MM=function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof Vm||wM(t,e.stroke,e.opacity)},IM=function(t,e,i,n){var r="fill"===e,a=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i.lineWidth)?(t[r?"filled":"stroked"]="true",i[e]instanceof Vm&&yM(t,a),a||(a=Ff(e)),r?SM(a,i,n):MM(a,i),mM(t,a)):(t[r?"filled":"stroked"]="false",yM(t,a))},TM=[[],[],[]],CM=function(t,e){var i,n,r,a,o,s,l=eM.M,h=eM.C,u=eM.L,c=eM.A,d=eM.Q,f=[],p=t.data,g=t.len();for(a=0;g>a;){switch(r=p[a++],n="",i=0,r){case l:n=" m ",i=1,o=p[a++],s=p[a++],TM[0][0]=o,TM[0][1]=s;break;case u:n=" l ",i=1,o=p[a++],s=p[a++],TM[0][0]=o,TM[0][1]=s;break;case d:case h:n=" c ",i=3;var v,m,y=p[a++],x=p[a++],_=p[a++],w=p[a++];r===d?(v=_,m=w,_=(_+2*y)/3,w=(w+2*x)/3,y=(o+2*y)/3,x=(s+2*x)/3):(v=p[a++],m=p[a++]),TM[0][0]=y,TM[0][1]=x,TM[1][0]=_,TM[1][1]=w,TM[2][0]=v,TM[2][1]=m,o=v,s=m;break;case c:var b=0,S=0,M=1,I=1,T=0;e&&(b=e[4],S=e[5],M=nM(e[0]*e[0]+e[1]*e[1]),I=nM(e[2]*e[2]+e[3]*e[3]),T=Math.atan2(-e[1]/I,e[0]/M));var C=p[a++],D=p[a++],A=p[a++],k=p[a++],P=p[a++]+T,L=p[a++]+P+T;a++;var O=p[a++],E=C+aM(P)*A,z=D+oM(P)*k,y=C+aM(L)*A,x=D+oM(L)*k,B=O?" wa ":" at ";Math.abs(E-y)<1e-4&&(Math.abs(L-P)>.01?O&&(E+=270/uM):Math.abs(z-D)<1e-4?O&&C>E||!O&&E>C?x-=270/uM:x+=270/uM:O&&D>z||!O&&z>D?y+=270/uM:y-=270/uM),f.push(B,iM(((C-A)*M+b)*uM-cM),lM,iM(((D-k)*I+S)*uM-cM),lM,iM(((C+A)*M+b)*uM-cM),lM,iM(((D+k)*I+S)*uM-cM),lM,iM((E*M+b)*uM-cM),lM,iM((z*I+S)*uM-cM),lM,iM((y*M+b)*uM-cM),lM,iM((x*I+S)*uM-cM)),o=y,s=x;break;case eM.R:var R=TM[0],N=TM[1];R[0]=p[a++],R[1]=p[a++],N[0]=R[0]+p[a++],N[1]=R[1]+p[a++],e&&(ae(R,R,e),ae(N,N,e)),R[0]=iM(R[0]*uM-cM),N[0]=iM(N[0]*uM-cM),R[1]=iM(R[1]*uM-cM),N[1]=iM(N[1]*uM-cM),f.push(" m ",R[0],lM,R[1]," l ",N[0],lM,R[1]," l ",N[0],lM,N[1]," l ",R[0],lM,N[1]);break;case eM.Z:f.push(" x ")}if(i>0){f.push(n);for(var F=0;i>F;F++){var V=TM[F];e&&ae(V,V,e),f.push(iM(V[0]*uM-cM),lM,iM(V[1]*uM-cM),i-1>F?lM:"")}}}return f.join("")};Qr.prototype.brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=Ff("shape"),pM(i),this._vmlEl=i),IM(i,"fill",e,this),IM(i,"stroke",e,this);var n=this.transform,r=null!=n,a=i.getElementsByTagName("stroke")[0];if(a){var o=e.lineWidth;if(r&&!e.strokeNoScale){var s=n[0]*n[3]-n[1]*n[2];o*=nM(rM(s))}a.weight=o+"px"}var l=this.path||(this.path=new Kv);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),i.path=CM(l,this.transform),i.style.zIndex=xM(this.zlevel,this.z,this.z2),mM(t,i),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},Qr.prototype.onRemove=function(t){yM(t,this._vmlEl),this.removeRectText(t)},Qr.prototype.onAdd=function(t){mM(t,this._vmlEl),this.appendRectText(t)};var DM=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};In.prototype.brushVML=function(t){var e,i,n=this.style,r=n.image;if(DM(r)){var a=r.src;if(a===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var o=r.runtimeStyle,s=o.width,l=o.height;o.width="auto",o.height="auto",e=r.width,i=r.height,o.width=s,o.height=l,this._imageSrc=a,this._imageWidth=e,this._imageHeight=i}r=a}else r===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(r){var h=n.x||0,u=n.y||0,c=n.width,d=n.height,f=n.sWidth,p=n.sHeight,g=n.sx||0,v=n.sy||0,m=f&&p,y=this._vmlEl;y||(y=JS.createElement("div"),pM(y),this._vmlEl=y);var x,_=y.style,w=!1,b=1,S=1;if(this.transform&&(x=this.transform,b=nM(x[0]*x[0]+x[1]*x[1]),S=nM(x[2]*x[2]+x[3]*x[3]),w=x[1]||x[2]),w){var M=[h,u],I=[h+c,u],T=[h,u+d],C=[h+c,u+d];ae(M,M,x),ae(I,I,x),ae(T,T,x),ae(C,C,x);var D=sM(M[0],I[0],T[0],C[0]),A=sM(M[1],I[1],T[1],C[1]),k=[];k.push("M11=",x[0]/b,lM,"M12=",x[2]/S,lM,"M21=",x[1]/b,lM,"M22=",x[3]/S,lM,"Dx=",iM(h*b+x[4]),lM,"Dy=",iM(u*S+x[5])),_.padding="0 "+iM(D)+"px "+iM(A)+"px 0",_.filter=hM+".Matrix("+k.join("")+", SizingMethod=clip)"
}else x&&(h=h*b+x[4],u=u*S+x[5]),_.filter="",_.left=iM(h)+"px",_.top=iM(u)+"px";var P=this._imageEl,L=this._cropEl;P||(P=JS.createElement("div"),this._imageEl=P);var O=P.style;if(m){if(e&&i)O.width=iM(b*e*c/f)+"px",O.height=iM(S*i*d/p)+"px";else{var E=new Image,z=this;E.onload=function(){E.onload=null,e=E.width,i=E.height,O.width=iM(b*e*c/f)+"px",O.height=iM(S*i*d/p)+"px",z._imageWidth=e,z._imageHeight=i,z._imageSrc=r},E.src=r}L||(L=JS.createElement("div"),L.style.overflow="hidden",this._cropEl=L);var B=L.style;B.width=iM((c+g*c/f)*b),B.height=iM((d+v*d/p)*S),B.filter=hM+".Matrix(Dx="+-g*c/f*b+",Dy="+-v*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!==L&&L.appendChild(P)}else O.width=iM(b*c)+"px",O.height=iM(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var R="",N=n.opacity;1>N&&(R+=".Alpha(opacity="+iM(100*N)+") "),R+=hM+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=xM(this.zlevel,this.z,this.z2),mM(t,y),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},In.prototype.onRemove=function(t){yM(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},In.prototype.onAdd=function(t){mM(t,this._vmlEl),this.appendRectText(t)};var AM,kM="normal",PM={},LM=0,OM=100,EM=document.createElement("div"),zM=function(t){var e=PM[t];if(!e){LM>OM&&(LM=0,PM={});var i,n=EM.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(r){}e={style:n.fontStyle||kM,variant:n.fontVariant||kM,weight:n.fontWeight||kM,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},PM[t]=e,LM++}return e};Vi("measureText",function(t,e){var i=JS;AM||(AM=i.createElement("div"),AM.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",JS.body.appendChild(AM));try{AM.style.font=e}catch(n){}return AM.innerHTML="",AM.appendChild(i.createTextNode(t)),{width:AM.offsetWidth}});for(var BM=new bi,RM=function(t,e,i,n){var r=this.style;this.__dirty&&on(r,!0);var a=r.text;if(null!=a&&(a+=""),a){if(r.rich){var o=en(a,r);a=[];for(var s=0;s<o.lines.length;s++){for(var l=o.lines[s].tokens,h=[],u=0;u<l.length;u++)h.push(l[u].text);a.push(h.join(""))}a=a.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=zM(r.font),v=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';i=i||Wi(a,v,f,p,r.textPadding,r.textLineHeight);var m=this.transform;if(m&&!n&&(BM.copy(e),BM.applyTransform(m),e=BM),n)c=e.x,d=e.y;else{var y=r.textPosition;if(y instanceof Array)c=e.x+_M(y[0],e.width),d=e.y+_M(y[1],e.height),f=f||"left";else{var x=this.calculateTextPosition?this.calculateTextPosition({},r,e):ji({},r,e);c=x.x,d=x.y,f=f||x.textAlign,p=p||x.textVerticalAlign}}c=Yi(c,i.width,f),d=Ui(d,i.height,p),d+=i.height/2;var _,w,b,S=Ff,M=this._textVmlEl;M?(b=M.firstChild,_=b.nextSibling,w=_.nextSibling):(M=S("line"),_=S("path"),w=S("textpath"),b=S("skew"),w.style["v-text-align"]="left",pM(M),_.textpathok=!0,w.on=!0,M.from="0 0",M.to="1000 0.05",mM(M,b),mM(M,_),mM(M,w),this._textVmlEl=M);var I=[c,d],T=M.style;m&&n?(ae(I,I,m),b.on=!0,b.matrix=m[0].toFixed(3)+lM+m[2].toFixed(3)+lM+m[1].toFixed(3)+lM+m[3].toFixed(3)+",0,0",b.offset=(iM(I[0])||0)+","+(iM(I[1])||0),b.origin="0 0",T.left="0px",T.top="0px"):(b.on=!1,T.left=iM(c)+"px",T.top=iM(d)+"px"),w.string=gM(a);try{w.style.font=v}catch(C){}IM(M,"fill",{fill:r.textFill,opacity:r.opacity},this),IM(M,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash||null},this),M.style.zIndex=xM(this.zlevel,this.z,this.z2),mM(t,M)}},NM=function(t){yM(t,this._textVmlEl),this._textVmlEl=null},FM=function(t){mM(t,this._textVmlEl)},VM=[Bg,Mn,In,Qr,wm],HM=0;HM<VM.length;HM++){var WM=VM[HM].prototype;WM.drawRectText=RM,WM.removeRectText=NM,WM.appendRectText=FM}wm.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},wm.prototype.onRemove=function(t){this.removeRectText(t)},wm.prototype.onAdd=function(t){this.appendRectText(t)}}Wf.prototype={constructor:Wf,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t.length;i++){var n=t[i];n.invisible||n.ignore?(n.__alreadyNotVisible||n.onRemove(e),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(e),n.__alreadyNotVisible=!1,n.__dirty&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,e),n.afterBrush&&n.afterBrush())),n.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||Hf(e.width))-Hf(e.paddingLeft)-Hf(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||Hf(e.height))-Hf(e.paddingTop)-Hf(e.paddingBottom)|0}},f(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){Wf.prototype[t]=Gf(t)}),Zn("vml",Wf),t.version=s_,t.dependencies=l_,t.PRIORITY=w_,t.init=lh,t.connect=hh,t.disConnect=uh,t.disconnect=H_,t.dispose=ch,t.getInstanceByDom=dh,t.getInstanceById=fh,t.registerTheme=ph,t.registerPreprocessor=gh,t.registerProcessor=vh,t.registerPostUpdate=mh,t.registerAction=yh,t.registerCoordinateSystem=xh,t.getCoordinateSystemDimensions=_h,t.registerLayout=wh,t.registerVisual=bh,t.registerLoading=Mh,t.extendComponentModel=Ih,t.extendComponentView=Th,t.extendSeriesModel=Ch,t.extendChartView=Dh,t.setCanvasCreator=Ah,t.registerMap=kh,t.getMap=Ph,t.dataTool=W_,t.zrender=rv,t.number=gy,t.format=by,t.throttle=dl,t.helper=Xw,t.matrix=Dp,t.vector=fp,t.color=jp,t.parseGeoJSON=Uw,t.parseGeoJson=Kw,t.util=$w,t.graphic=Qw,t.List=tw,t.Model=uo,t.Axis=Zw,t.env=jf});