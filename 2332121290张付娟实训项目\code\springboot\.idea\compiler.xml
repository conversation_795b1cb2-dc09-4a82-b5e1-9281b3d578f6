<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="nongchanpinxiaoshou" />
        <module name="springboot" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="17" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="nongchanpinxiaoshou" options="-parameters" />
      <module name="springboot" options="-parameters" />
    </option>
  </component>
</project>